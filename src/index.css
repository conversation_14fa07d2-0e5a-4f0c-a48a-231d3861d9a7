@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern gradient background - Notion/Duolingo style */
body {
  background: linear-gradient(
    135deg,
    #f8fafc 0%,
    #f1f5f9 15%,
    #e2e8f0 30%,
    #f1f5f9 45%,
    #f8fafc 60%,
    #f0f4ff 75%,
    #e6f3ff 90%,
    #f8fafc 100%
  );
  min-height: 100vh;
  position: relative;
}

/* Ensure the gradient covers the full app */
#root {
  min-height: 100vh;
  background: inherit;
}

/* Enhanced overlay with visible brand colors */
.main-content {
  background: linear-gradient(
    135deg,
    rgba(99, 102, 241, 0.03) 0%,
    rgba(168, 85, 247, 0.025) 20%,
    rgba(236, 72, 153, 0.02) 40%,
    rgba(59, 130, 246, 0.025) 60%,
    rgba(16, 185, 129, 0.02) 80%,
    rgba(99, 102, 241, 0.015) 100%
  );
  position: relative;
  min-height: 100vh;
}

/* Floating shapes for modern feel */
.main-content::before {
  content: '';
  position: fixed;
  top: 15%;
  right: 8%;
  width: 300px;
  height: 300px;
  background: radial-gradient(
    circle,
    rgba(99, 102, 241, 0.08) 0%,
    rgba(168, 85, 247, 0.06) 30%,
    rgba(99, 102, 241, 0.04) 60%,
    transparent 80%
  );
  border-radius: 50%;
  animation: float-gentle 25s ease-in-out infinite;
  pointer-events: none;
  z-index: 0;
  filter: blur(1px);
}

.main-content::after {
  content: '';
  position: fixed;
  bottom: 10%;
  left: 5%;
  width: 250px;
  height: 250px;
  background: radial-gradient(
    circle,
    rgba(236, 72, 153, 0.06) 0%,
    rgba(59, 130, 246, 0.05) 40%,
    rgba(16, 185, 129, 0.04) 70%,
    transparent 85%
  );
  border-radius: 50%;
  animation: float-gentle 30s ease-in-out infinite reverse;
  pointer-events: none;
  z-index: 0;
  filter: blur(1px);
}

/* Add more floating elements using CSS custom properties */
body::before {
  content: '';
  position: fixed;
  top: 40%;
  right: 25%;
  width: 120px;
  height: 120px;
  background: radial-gradient(
    circle,
    rgba(168, 85, 247, 0.05) 0%,
    rgba(99, 102, 241, 0.03) 50%,
    transparent 75%
  );
  border-radius: 50%;
  animation: float-slow 35s ease-in-out infinite;
  pointer-events: none;
  z-index: 0;
  filter: blur(0.5px);
}

body::after {
  content: '';
  position: fixed;
  top: 60%;
  left: 70%;
  width: 180px;
  height: 180px;
  background: radial-gradient(
    circle,
    rgba(16, 185, 129, 0.04) 0%,
    rgba(59, 130, 246, 0.03) 60%,
    transparent 80%
  );
  border-radius: 50%;
  animation: float-slow 40s ease-in-out infinite reverse;
  pointer-events: none;
  z-index: 0;
  filter: blur(0.8px);
}

@keyframes float-gentle {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-15px) translateX(8px) rotate(90deg);
    opacity: 0.9;
  }
  50% {
    transform: translateY(-25px) translateX(-5px) rotate(180deg);
    opacity: 0.6;
  }
  75% {
    transform: translateY(-10px) translateX(12px) rotate(270deg);
    opacity: 0.8;
  }
}

@keyframes float-slow {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.5;
  }
  33% {
    transform: translateY(-30px) translateX(15px) scale(1.1);
    opacity: 0.7;
  }
  66% {
    transform: translateY(-10px) translateX(-20px) scale(0.9);
    opacity: 0.6;
  }
}

/* Add subtle wave pattern */
.main-content {
  background-image:
    radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.025) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(236, 72, 153, 0.02) 0%, transparent 50%);
  background-size: 800px 800px, 600px 600px, 400px 400px;
  background-position: 0% 0%, 100% 100%, 50% 50%;
  animation: wave-movement 60s ease-in-out infinite;
}

@keyframes wave-movement {
  0%, 100% {
    background-position: 0% 0%, 100% 100%, 50% 50%;
  }
  25% {
    background-position: 10% 10%, 90% 90%, 60% 40%;
  }
  50% {
    background-position: 20% 5%, 80% 95%, 40% 60%;
  }
  75% {
    background-position: 5% 15%, 95% 85%, 55% 45%;
  }
}

/* Ensure content stays above the floating elements */
.main-content > * {
  position: relative;
  z-index: 1;
}

/* Enhanced card backgrounds for better contrast */
.main-content .bg-white,
.main-content .bg-card,
.main-content [class*="bg-white"] {
  background-color: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
}

/* Enhance sidebar and navigation */
.main-content nav,
.main-content aside,
.main-content [role="navigation"] {
  background-color: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(15px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

/* Enhance buttons and interactive elements */
.main-content button:hover,
.main-content .hover\\:bg-accent:hover {
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.15);
  transform: translateY(-1px);
  transition: all 0.3s ease;
}

/* Enhance form inputs */
.main-content input,
.main-content textarea,
.main-content select {
  background-color: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(99, 102, 241, 0.1);
}

.main-content input:focus,
.main-content textarea:focus,
.main-content select:focus {
  border-color: rgba(99, 102, 241, 0.3);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Responsive design for tablets */
@media (max-width: 768px) {
  .main-content::before {
    width: 200px;
    height: 200px;
    top: 10%;
    right: 5%;
  }

  .main-content::after {
    width: 150px;
    height: 150px;
    bottom: 15%;
    left: 3%;
  }

  body::before {
    width: 80px;
    height: 80px;
  }

  body::after {
    width: 120px;
    height: 120px;
  }
}

/* Mobile optimizations */
@media (max-width: 480px) {
  body {
    background: linear-gradient(
      135deg,
      #f8fafc 0%,
      #f1f5f9 25%,
      #e2e8f0 50%,
      #f1f5f9 75%,
      #f8fafc 100%
    );
  }

  .main-content::before,
  .main-content::after {
    display: none; /* Hide some elements on small mobile for performance */
  }

  body::before {
    width: 60px;
    height: 60px;
    opacity: 0.3;
  }

  body::after {
    width: 80px;
    height: 80px;
    opacity: 0.3;
  }
}

/* RTL support */
[dir="rtl"] .main-content::before {
  right: auto;
  left: 8%;
}

[dir="rtl"] .main-content::after {
  left: auto;
  right: 5%;
}

[dir="rtl"] body::before {
  right: auto;
  left: 25%;
}

[dir="rtl"] body::after {
  left: auto;
  right: 70%;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  body,
  .main-content {
    background: #ffffff !important;
  }

  .main-content::before,
  .main-content::after,
  body::before,
  body::after {
    display: none;
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .main-content::before,
  .main-content::after,
  body::before,
  body::after,
  .main-content {
    animation: none !important;
  }
}

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Premium color palette for trustworthy appearance */
    --background: 0 0% 100%;
    --foreground: 220 13% 18%;

    --card: 0 0% 100%;
    --card-foreground: 220 13% 18%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 18%;

    /* Professional blue-gray primary */
    --primary: 217 19% 27%;
    --primary-foreground: 0 0% 98%;
    --primary-hover: 217 19% 35%;

    /* Soft secondary colors */
    --secondary: 220 14% 96%;
    --secondary-foreground: 220 13% 18%;

    --muted: 220 14% 96%;
    --muted-foreground: 220 9% 46%;

    /* Refined accent color */
    --accent: 214 32% 91%;
    --accent-foreground: 217 19% 27%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    /* Subtle borders and inputs */
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 217 19% 27%;

    --radius: 0.75rem;

    /* Enhanced sidebar with better contrast */
    --sidebar-background: 220 14% 98%;
    --sidebar-foreground: 220 13% 18%;
    --sidebar-primary: 217 19% 27%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 220 14% 95%;
    --sidebar-accent-foreground: 217 19% 27%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217 19% 27%;

    /* Premium shadows and effects */
    --shadow-sm: 0 1px 2px 0 hsl(220 13% 18% / 0.05);
    --shadow: 0 1px 3px 0 hsl(220 13% 18% / 0.1), 0 1px 2px -1px hsl(220 13% 18% / 0.1);
    --shadow-md: 0 4px 6px -1px hsl(220 13% 18% / 0.1), 0 2px 4px -2px hsl(220 13% 18% / 0.1);
    --shadow-lg: 0 10px 15px -3px hsl(220 13% 18% / 0.1), 0 4px 6px -4px hsl(220 13% 18% / 0.1);
    --shadow-xl: 0 20px 25px -5px hsl(220 13% 18% / 0.1), 0 8px 10px -6px hsl(220 13% 18% / 0.1);

    /* Success and warning colors */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter antialiased;
    font-feature-settings: "cv11", "ss01";
    font-variation-settings: "opsz" 32;
  }

  /* Enhanced typography */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-2xl md:text-3xl lg:text-4xl;
  }

  h2 {
    @apply text-xl md:text-2xl lg:text-3xl;
  }

  h3 {
    @apply text-lg md:text-xl lg:text-2xl;
  }

  /* Better button focus states */
  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }

  /* Enhanced card styles */
  .card-elevated {
    @apply shadow-md hover:shadow-lg transition-all duration-300;
  }

  .card-interactive {
    @apply hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300;
  }

  /* Smooth animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Better scrollbars */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }
}