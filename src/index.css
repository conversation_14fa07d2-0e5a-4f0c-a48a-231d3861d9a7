@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern, warm, and elegant background */
body {
  background: linear-gradient(
    135deg,
    #fef7f0 0%,
    #fdf4f0 10%,
    #fef2ec 20%,
    #fff0e6 30%,
    #f0f9ff 40%,
    #e0f2fe 50%,
    #e6fffa 60%,
    #f0fdfa 70%,
    #f7fee7 80%,
    #fefce8 90%,
    #fef7f0 100%
  ) !important;
  min-height: 100vh;
  position: relative;
}

/* Elegant floating orbs with warm colors */
body::before {
  content: '';
  position: fixed;
  top: -10%;
  right: -10%;
  width: 600px;
  height: 600px;
  background: radial-gradient(
    circle,
    rgba(251, 146, 60, 0.15) 0%,
    rgba(249, 115, 22, 0.1) 30%,
    rgba(245, 101, 101, 0.08) 60%,
    transparent 80%
  );
  border-radius: 50%;
  animation: float-elegant 40s ease-in-out infinite;
  pointer-events: none;
  z-index: 0;
  filter: blur(2px);
}

body::after {
  content: '';
  position: fixed;
  bottom: -15%;
  left: -15%;
  width: 700px;
  height: 700px;
  background: radial-gradient(
    circle,
    rgba(59, 130, 246, 0.12) 0%,
    rgba(99, 102, 241, 0.08) 40%,
    rgba(139, 92, 246, 0.06) 70%,
    transparent 85%
  );
  border-radius: 50%;
  animation: float-elegant 50s ease-in-out infinite reverse;
  pointer-events: none;
  z-index: 0;
  filter: blur(3px);
}

/* Additional floating elements for depth */
.modern-bg-accent {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.08) 0%, transparent 40%),
    radial-gradient(circle at 20% 80%, rgba(168, 85, 247, 0.06) 0%, transparent 40%),
    radial-gradient(circle at 60% 60%, rgba(251, 191, 36, 0.05) 0%, transparent 30%);
  pointer-events: none;
  z-index: 1;
  animation: gentle-pulse 60s ease-in-out infinite;
}

@keyframes float-elegant {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
    opacity: 0.8;
  }
  25% {
    transform: translateY(-30px) translateX(20px) rotate(90deg) scale(1.05);
    opacity: 1;
  }
  50% {
    transform: translateY(-50px) translateX(-10px) rotate(180deg) scale(0.95);
    opacity: 0.7;
  }
  75% {
    transform: translateY(-20px) translateX(30px) rotate(270deg) scale(1.02);
    opacity: 0.9;
  }
}

@keyframes gentle-pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  body::before {
    width: 400px;
    height: 400px;
    top: -5%;
    right: -5%;
  }

  body::after {
    width: 500px;
    height: 500px;
    bottom: -10%;
    left: -10%;
  }

  .sidebar {
    backdrop-filter: blur(15px) saturate(160%) !important;
  }
}

@media (max-width: 480px) {
  body::before {
    width: 300px;
    height: 300px;
    opacity: 0.6;
  }

  body::after {
    width: 350px;
    height: 350px;
    opacity: 0.6;
  }

  .sidebar {
    backdrop-filter: blur(10px) saturate(140%) !important;
    background: rgba(255, 255, 255, 0.9) !important;
  }

  .modern-bg-accent {
    opacity: 0.7;
  }
}

/* RTL support */
[dir="rtl"] body::before {
  right: auto;
  left: -10%;
}

[dir="rtl"] body::after {
  left: auto;
  right: -15%;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  body::before,
  body::after,
  .modern-bg-accent {
    animation: none !important;
  }
}

@media (prefers-contrast: high) {
  body {
    background: #ffffff !important;
  }

  body::before,
  body::after,
  .modern-bg-accent {
    display: none;
  }

  .sidebar {
    background: #ffffff !important;
    backdrop-filter: none !important;
    border-right: 2px solid #000000 !important;
  }
}

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Premium color palette for trustworthy appearance */
    --background: 220 14% 96%;
    --foreground: 220 13% 18%;

    --card: 0 0% 100%;
    --card-foreground: 220 13% 18%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 18%;

    /* Professional blue-gray primary */
    --primary: 217 19% 27%;
    --primary-foreground: 0 0% 98%;
    --primary-hover: 217 19% 35%;

    /* Soft secondary colors */
    --secondary: 220 14% 96%;
    --secondary-foreground: 220 13% 18%;

    --muted: 220 14% 96%;
    --muted-foreground: 220 9% 46%;

    /* Refined accent color */
    --accent: 214 32% 91%;
    --accent-foreground: 217 19% 27%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    /* Subtle borders and inputs */
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 217 19% 27%;

    --radius: 0.75rem;

    /* Modern glass sidebar */
    --sidebar-background: 255 255 255 / 0.85;
    --sidebar-foreground: 220 13% 18%;
    --sidebar-primary: 217 19% 27%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 255 255 255 / 0.6;
    --sidebar-accent-foreground: 217 19% 27%;
    --sidebar-border: 255 255 255 / 0.3;
    --sidebar-ring: 217 19% 27%;

    /* Premium shadows and effects */
    --shadow-sm: 0 1px 2px 0 hsl(220 13% 18% / 0.05);
    --shadow: 0 1px 3px 0 hsl(220 13% 18% / 0.1), 0 1px 2px -1px hsl(220 13% 18% / 0.1);
    --shadow-md: 0 4px 6px -1px hsl(220 13% 18% / 0.1), 0 2px 4px -2px hsl(220 13% 18% / 0.1);
    --shadow-lg: 0 10px 15px -3px hsl(220 13% 18% / 0.1), 0 4px 6px -4px hsl(220 13% 18% / 0.1);
    --shadow-xl: 0 20px 25px -5px hsl(220 13% 18% / 0.1), 0 8px 10px -6px hsl(220 13% 18% / 0.1);

    /* Success and warning colors */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply text-foreground font-inter antialiased;
    font-feature-settings: "cv11", "ss01";
    font-variation-settings: "opsz" 32;
  }

  /* Enhanced typography */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-2xl md:text-3xl lg:text-4xl;
  }

  h2 {
    @apply text-xl md:text-2xl lg:text-3xl;
  }

  h3 {
    @apply text-lg md:text-xl lg:text-2xl;
  }

  /* Better button focus states */
  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }

  /* Enhanced card styles */
  .card-elevated {
    @apply shadow-md hover:shadow-lg transition-all duration-300;
  }

  .card-interactive {
    @apply hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300;
  }

  /* Smooth animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Modern glass morphism effects */
  .sidebar {
    background: rgba(255, 255, 255, 0.85) !important;
    backdrop-filter: blur(20px) saturate(180%);
    border-right: 1px solid rgba(255, 255, 255, 0.3) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  /* Header glass effect */
  header, .top-bar {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(15px) saturate(180%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  }

  /* Enhanced cards with glass effect */
  .card, [data-card] {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(10px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  }

  /* Better scrollbars */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
  }
}