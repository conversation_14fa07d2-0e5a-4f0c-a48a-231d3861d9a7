@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern background that complements the app's teal/blue/purple theme */
body {
  background: linear-gradient(
    135deg,
    #f1f5f9 0%,
    #e2e8f0 25%,
    #cbd5e1 50%,
    #e2e8f0 75%,
    #f1f5f9 100%
  ) !important;
  min-height: 100vh;
}

/* Subtle overlay with app's brand colors */
.app-background-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 20%, rgba(20, 184, 166, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(147, 51, 234, 0.08) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* Floating elements that match the brand colors */
.app-background-overlay::before {
  content: '';
  position: absolute;
  top: 10%;
  right: 15%;
  width: 300px;
  height: 300px;
  background: radial-gradient(
    circle,
    rgba(20, 184, 166, 0.15) 0%,
    rgba(59, 130, 246, 0.12) 50%,
    transparent 80%
  );
  border-radius: 50%;
  animation: float-gentle 30s ease-in-out infinite;
  filter: blur(1px);
}

.app-background-overlay::after {
  content: '';
  position: absolute;
  bottom: 15%;
  left: 10%;
  width: 250px;
  height: 250px;
  background: radial-gradient(
    circle,
    rgba(147, 51, 234, 0.12) 0%,
    rgba(59, 130, 246, 0.10) 50%,
    transparent 80%
  );
  border-radius: 50%;
  animation: float-gentle 35s ease-in-out infinite reverse;
  filter: blur(1px);
}

@keyframes float-gentle {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20px) translateX(10px);
    opacity: 0.8;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .app-background-overlay::before {
    width: 200px;
    height: 200px;
    top: 5%;
    right: 10%;
  }

  .app-background-overlay::after {
    width: 150px;
    height: 150px;
    bottom: 10%;
    left: 5%;
  }
}

@media (max-width: 480px) {
  .app-background-overlay::before,
  .app-background-overlay::after {
    width: 100px;
    height: 100px;
    opacity: 0.5;
  }
}

/* RTL support */
[dir="rtl"] .app-background-overlay::before {
  right: auto;
  left: 15%;
}

[dir="rtl"] .app-background-overlay::after {
  left: auto;
  right: 10%;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .app-background-overlay::before,
  .app-background-overlay::after {
    animation: none;
  }
}

@media (prefers-contrast: high) {
  body {
    background: hsl(0, 0%, 100%);
  }

  .app-background-overlay {
    display: none;
  }
}

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Premium color palette for trustworthy appearance */
    --background: 220 14% 96%;
    --foreground: 220 13% 18%;

    --card: 0 0% 100%;
    --card-foreground: 220 13% 18%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 18%;

    /* Professional blue-gray primary */
    --primary: 217 19% 27%;
    --primary-foreground: 0 0% 98%;
    --primary-hover: 217 19% 35%;

    /* Soft secondary colors */
    --secondary: 220 14% 96%;
    --secondary-foreground: 220 13% 18%;

    --muted: 220 14% 96%;
    --muted-foreground: 220 9% 46%;

    /* Refined accent color */
    --accent: 214 32% 91%;
    --accent-foreground: 217 19% 27%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    /* Subtle borders and inputs */
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 217 19% 27%;

    --radius: 0.75rem;

    /* Enhanced sidebar with better contrast */
    --sidebar-background: 220 14% 98%;
    --sidebar-foreground: 220 13% 18%;
    --sidebar-primary: 217 19% 27%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 220 14% 95%;
    --sidebar-accent-foreground: 217 19% 27%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217 19% 27%;

    /* Premium shadows and effects */
    --shadow-sm: 0 1px 2px 0 hsl(220 13% 18% / 0.05);
    --shadow: 0 1px 3px 0 hsl(220 13% 18% / 0.1), 0 1px 2px -1px hsl(220 13% 18% / 0.1);
    --shadow-md: 0 4px 6px -1px hsl(220 13% 18% / 0.1), 0 2px 4px -2px hsl(220 13% 18% / 0.1);
    --shadow-lg: 0 10px 15px -3px hsl(220 13% 18% / 0.1), 0 4px 6px -4px hsl(220 13% 18% / 0.1);
    --shadow-xl: 0 20px 25px -5px hsl(220 13% 18% / 0.1), 0 8px 10px -6px hsl(220 13% 18% / 0.1);

    /* Success and warning colors */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply text-foreground font-inter antialiased;
    background: linear-gradient(
      135deg,
      #f1f5f9 0%,
      #e2e8f0 25%,
      #cbd5e1 50%,
      #e2e8f0 75%,
      #f1f5f9 100%
    ) !important;
    min-height: 100vh;
    font-feature-settings: "cv11", "ss01";
    font-variation-settings: "opsz" 32;
  }

  /* Enhanced typography */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-2xl md:text-3xl lg:text-4xl;
  }

  h2 {
    @apply text-xl md:text-2xl lg:text-3xl;
  }

  h3 {
    @apply text-lg md:text-xl lg:text-2xl;
  }

  /* Better button focus states */
  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }

  /* Enhanced card styles */
  .card-elevated {
    @apply shadow-md hover:shadow-lg transition-all duration-300;
  }

  .card-interactive {
    @apply hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300;
  }

  /* Smooth animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Better scrollbars */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }
}