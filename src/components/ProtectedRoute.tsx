
import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { usePhoneVerification } from '@/hooks/usePhoneVerification';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { user, loading: authLoading } = useAuth();
  const { needsPhoneVerification, loading: phoneLoading } = usePhoneVerification();
  const navigate = useNavigate();
  const location = useLocation();



  useEffect(() => {
    console.log('🔄 ProtectedRoute useEffect triggered');

    if (!authLoading && !user) {
      console.log('❌ No user found, redirecting to signin');
      navigate('/signin');
      return;
    }

    // If user is authenticated but needs phone verification
    // and they're not already on the phone verification page
    if (
      user &&
      !phoneLoading &&
      needsPhoneVerification &&
      location.pathname !== '/phone-verification'
    ) {
      console.log('📱 Redirecting to phone verification');
      navigate('/phone-verification');
    }
  }, [user, authLoading, needsPhoneVerification, phoneLoading, navigate, location.pathname]);

  if (authLoading || phoneLoading) {
    console.log('⏳ Showing loading state');
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-teal-500 mx-auto mb-4"></div>
          <p className="text-slate-600">Loading authentication...</p>
          <p className="text-xs text-slate-400 mt-2">
            Auth: {authLoading ? 'Loading...' : 'Ready'} |
            Phone: {phoneLoading ? 'Loading...' : 'Ready'}
          </p>
        </div>
      </div>
    );
  }

  if (!user) {
    console.log('🚫 No user, showing redirect message');
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-50">
        <div className="text-center">
          <p className="text-slate-600">Redirecting to sign in...</p>
        </div>
      </div>
    );
  }

  console.log('✅ User authenticated, rendering children');
  return <>{children}</>;
};
