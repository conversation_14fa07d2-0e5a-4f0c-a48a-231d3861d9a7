import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Send, FileText } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface UploadedFile {
  id: string;
  file: File;
  name: string;
  size: number;
}

export const ChatInterface = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();
  const { t, isRTL, direction } = useLanguage();

  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [hasUserInput, setHasUserInput] = useState(false);
  const [currentBotId, setCurrentBotId] = useState<string | null>(null);
  const [chatHistory, setChatHistory] = useState<string[]>([]); // Store user messages for combined saving
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Initialize greeting message with translation
  useEffect(() => {
    if (messages.length === 0) {
      setMessages([{
        id: '1',
        type: 'assistant',
        content: t('chatbotGreeting'),
        timestamp: new Date()
      }]);
    }
  }, [t, messages.length]);

  // Check if storage bucket exists and is accessible
  const checkStorageBucket = async () => {
    try {
      console.log('Checking storage bucket...');
      const { data, error } = await supabase.storage.getBucket('documents');
      if (error) {
        console.error('Storage bucket check failed:', error);
        console.error('Error details:', error);
        return false;
      }
      console.log('Storage bucket exists and is accessible:', data);
      return true;
    } catch (error) {
      console.error('Storage bucket check error:', error);
      return false;
    }
  };

  // Test storage connection
  const testStorageConnection = async () => {
    console.log('Testing storage connection...');
    console.log('User:', user?.id);

    const bucketExists = await checkStorageBucket();

    if (bucketExists) {
      toast({
        title: "Storage Test",
        description: "Storage bucket is accessible!",
      });
    } else {
      toast({
        title: "Storage Test Failed",
        description: "Cannot access storage bucket. Check console for details.",
        variant: "destructive",
      });
    }
  };

  // Initialize storage check on component mount
  useEffect(() => {
    if (user) {
      checkStorageBucket();
    }
  }, [user]);

  // Helper function to save combined chat history as a document
  const saveChatDocument = async (combinedChatHistory: string) => {
    if (!user) {
      console.log('User not authenticated, skipping chat document save');
      return null;
    }

    if (!combinedChatHistory.trim()) {
      console.log('No chat history to save');
      return null;
    }

    try {
      console.log('Saving chat document with content:', combinedChatHistory);

      const { data, error } = await supabase
        .from('documents')
        .insert({
          user_id: user.id,
          bot_id: currentBotId,
          file_name: 'Chat Description',
          file_url: null,
          content: combinedChatHistory,
          type: 'chat',
          embedding_status: 'pending'
        })
        .select()
        .single();

      if (error) {
        console.error('Error saving chat document:', error);
        toast({
          title: "Chat Save Error",
          description: "Failed to save chat history. Please try again.",
          variant: "destructive",
        });
        return null;
      }

      console.log('Chat document saved successfully:', data);
      return data;
    } catch (error) {
      console.error('Error saving chat document:', error);
      toast({
        title: "Chat Save Error",
        description: "Failed to save chat history. Please try again.",
        variant: "destructive",
      });
      return null;
    }
  };

  // Helper function to upload file to Supabase Storage and save document record
  const saveFileDocument = async (file: File) => {
    if (!user) {
      console.log('User not authenticated, skipping file document save');
      return null;
    }

    try {
      console.log('Starting file upload for:', file.name);
      console.log('User ID:', user.id);

      // Generate UUID for unique file path
      const fileExt = file.name.split('.').pop();
      const fileUuid = crypto.randomUUID();
      const fileName = `${user.id}/${fileUuid}-${file.name}`;

      console.log('Uploading to path:', fileName);
      console.log('File size:', file.size, 'bytes');
      console.log('File type:', file.type);

      // Upload file to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('documents')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        console.error('Storage upload error:', uploadError);
        toast({
          title: "Upload Failed",
          description: `Storage error: ${uploadError.message}`,
          variant: "destructive",
        });
        return null;
      }

      console.log('File uploaded successfully:', uploadData);

      // Get file type based on extension
      const getFileType = (extension: string) => {
        switch (extension?.toLowerCase()) {
          case 'pdf': return 'pdf';
          case 'txt': return 'txt';
          case 'docx': return 'docx';
          default: return 'txt';
        }
      };

      // Get public URL for the uploaded file
      const { data: urlData } = supabase.storage
        .from('documents')
        .getPublicUrl(uploadData.path);

      console.log('File public URL:', urlData.publicUrl);

      // Save document record to database
      const { data, error } = await supabase
        .from('documents')
        .insert({
          user_id: user.id,
          bot_id: currentBotId,
          file_name: file.name,
          file_url: urlData.publicUrl,
          content: null, // Files don't have text content in this field
          type: getFileType(fileExt || ''),
          embedding_status: 'pending'
        })
        .select()
        .single();

      if (error) {
        console.error('Database insert error:', error);
        toast({
          title: "Database Error",
          description: `Failed to save file record: ${error.message}`,
          variant: "destructive",
        });
        return null;
      }

      console.log('File document saved successfully:', data);
      return data;
    } catch (error) {
      console.error('Error saving file document:', error);
      toast({
        title: "File Save Error",
        description: "An unexpected error occurred while saving the file.",
        variant: "destructive",
      });
      return null;
    }
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, newMessage]);
    const messageContent = inputValue;

    // Add user message to chat history for later saving
    setChatHistory(prev => [...prev, messageContent]);

    setInputValue("");
    setHasUserInput(true);

    console.log('Added message to chat history:', messageContent);
    console.log('Current chat history:', [...chatHistory, messageContent]);

    // Simulate assistant response after a brief delay
    setTimeout(() => {
      const responses = [
        "That sounds interesting! Tell me more about your services.",
        "Great! What specific questions do your customers usually ask?",
        "Perfect! Any particular tone or style you'd like your chatbot to have?",
        "Excellent! What else should I know about your business?"
      ];

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: responses[Math.floor(Math.random() * responses.length)],
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);
    }, 1000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('File selection started');
    console.log('User authenticated:', !!user);

    if (e.target.files) {
      const files = Array.from(e.target.files);
      console.log('Selected files:', files.map(f => ({ name: f.name, type: f.type, size: f.size })));

      const validFiles = files.filter(file =>
        ['application/pdf', 'text/plain', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(file.type)
      );

      console.log('Valid files after filtering:', validFiles.map(f => f.name));

      if (validFiles.length === 0) {
        toast({
          title: t('fileUploadFailed'),
          description: t('unsupportedFileType'),
          variant: "destructive",
        });
        return;
      }

      for (const file of validFiles) {
        console.log(`Processing file: ${file.name}`);
        try {
          // Save file to database and storage
          const savedDocument = await saveFileDocument(file);

          if (savedDocument) {
            const uploadedFile: UploadedFile = {
              id: savedDocument.id,
              file,
              name: file.name,
              size: file.size
            };

            setUploadedFiles(prev => [...prev, uploadedFile]);
            setHasUserInput(true);

            // Add file upload message
            const fileMessage: Message = {
              id: Date.now().toString() + Math.random(),
              type: 'user',
              content: `📄 \`${file.name}\` uploaded successfully`,
              timestamp: new Date()
            };

            setMessages(prev => [...prev, fileMessage]);

            // Add file upload to chat history for bot training
            const fileHistoryEntry = `File uploaded: ${file.name} (${file.type})`;
            setChatHistory(prev => [...prev, fileHistoryEntry]);
            console.log('Added file to chat history:', fileHistoryEntry);

            // Assistant acknowledgment
            setTimeout(() => {
              const ackMessage: Message = {
                id: Date.now().toString() + Math.random(),
                type: 'assistant',
                content: "Great! I've received your file and it's being processed. Feel free to tell me more about your business or upload additional files.",
                timestamp: new Date()
              };
              setMessages(prev => [...prev, ackMessage]);
            }, 500);
          } else {
            // If user is not authenticated, still show the file in UI but inform about sign up
            if (!user) {
              const uploadedFile: UploadedFile = {
                id: Date.now().toString() + Math.random(),
                file,
                name: file.name,
                size: file.size
              };

              setUploadedFiles(prev => [...prev, uploadedFile]);
              setHasUserInput(true);

              // Add file upload message
              const fileMessage: Message = {
                id: Date.now().toString() + Math.random(),
                type: 'user',
                content: `📄 \`${file.name}\` ready for upload`,
                timestamp: new Date()
              };

              setMessages(prev => [...prev, fileMessage]);

              // Add file to chat history even for unauthenticated users
              const fileHistoryEntry = `File ready for upload: ${file.name} (${file.type})`;
              setChatHistory(prev => [...prev, fileHistoryEntry]);
              console.log('Added file to chat history (unauthenticated):', fileHistoryEntry);

              // Assistant acknowledgment
              setTimeout(() => {
                const ackMessage: Message = {
                  id: Date.now().toString() + Math.random(),
                  type: 'assistant',
                  content: "I can see your file! When you create your chatbot, this file will be uploaded and processed for training.",
                  timestamp: new Date()
                };
                setMessages(prev => [...prev, ackMessage]);
              }, 500);
            } else {
              // Show error message if file upload failed for authenticated users
              toast({
                title: "Upload Failed",
                description: `Failed to upload ${file.name}. Please try again.`,
                variant: "destructive",
              });
            }
          }
        } catch (error) {
          console.error('Error uploading file:', error);
          toast({
            title: "Upload Error",
            description: `Error uploading ${file.name}. Please try again.`,
            variant: "destructive",
          });
        }
      }
    }
  };

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  const handleGenerateBot = async () => {
    if (!hasUserInput) return;

    // If user is not authenticated, redirect to sign up
    if (!user) {
      toast({
        title: "Sign Up Required",
        description: "Please sign up to create your chatbot and save your conversation.",
      });
      navigate('/signup');
      return;
    }

    try {
      console.log('Starting chatbot generation...');
      console.log('Chat history array:', chatHistory);
      console.log('Chat history length:', chatHistory.length);
      console.log('Has user input:', hasUserInput);
      console.log('Uploaded files:', uploadedFiles.map(f => f.name));

      // Show what we're about to save
      if (chatHistory.length > 0) {
        const combinedChatHistory = chatHistory.join('\n\n');
        console.log('Combined chat history to save:', combinedChatHistory);

        toast({
          title: "Generating Your Chatbot",
          description: `Saving ${chatHistory.length} messages and ${uploadedFiles.length} files...`,
        });
      } else {
        toast({
          title: "Generating Your Chatbot",
          description: "Creating your personalized chatbot...",
        });
      }

      // Save combined chat history as a single document
      if (chatHistory.length > 0) {
        const combinedChatHistory = chatHistory.join('\n\n');
        console.log('Saving combined chat history:', combinedChatHistory);

        const savedChatDocument = await saveChatDocument(combinedChatHistory);
        if (savedChatDocument) {
          console.log('Chat document saved successfully:', savedChatDocument);
        } else {
          console.error('Failed to save chat document');
        }
      } else {
        console.log('No chat history to save - array is empty');
        console.log('Current messages:', messages.filter(m => m.type === 'user'));
      }

      // For now, simulate the bot creation process
      // In a real implementation, you would:
      // 1. Create a bot record in the bots table
      // 2. Update all documents with the new bot_id
      // 3. Trigger the embedding process

      setTimeout(async () => {
        toast({
          title: t('chatbotCreated'),
          description: t('chatbotCreatedDesc'),
        });

        navigate('/dashboard');
      }, 2000);

    } catch (error) {
      console.error('Error generating bot:', error);
      toast({
        title: "Error",
        description: "Failed to create chatbot. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="bg-white rounded-2xl shadow-2xl overflow-hidden ring-1 ring-slate-200" dir={direction}>
      {/* Header */}
      <div className="bg-gradient-to-r from-teal-500 via-blue-500 to-purple-600 p-8 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent"></div>
        <div className="relative z-10">
          <h2 className="text-3xl font-bold mb-3 drop-shadow-sm">
            Let's Build Your Chatbot
          </h2>
          <p className="text-white/90 text-lg">
            Chat with me about your business and upload relevant files
          </p>
        </div>
      </div>

      {/* Chat Messages */}
      <div className="h-96 overflow-y-auto p-6 space-y-4 bg-gradient-to-b from-slate-50 to-white">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} animate-fade-in`}
          >
            <div
              className={`max-w-[80%] p-4 rounded-2xl shadow-lg ${
                message.type === 'user'
                  ? 'bg-gradient-to-r from-teal-500 to-blue-600 text-white ml-4'
                  : 'bg-white border border-slate-200 mr-4 shadow-md'
              }`}
            >
              <p className="text-sm leading-relaxed">{message.content}</p>
              <span className={`text-xs opacity-70 mt-2 block ${
                message.type === 'user' ? 'text-white/70' : 'text-slate-500'
              }`}>
                {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </span>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Uploaded Files Display */}
      {uploadedFiles.length > 0 && (
        <div className="px-6 py-3 border-t border-border bg-muted/10">
          <div className="flex flex-wrap gap-2">
            {uploadedFiles.map((file) => (
              <div
                key={file.id}
                className="flex items-center gap-2 bg-success/10 text-success-foreground px-3 py-1 rounded-full text-sm"
              >
                <FileText className="w-4 h-4" />
                <span>{file.name}</span>
                <span className="text-xs opacity-70">
                  ({(file.size / 1024 / 1024).toFixed(1)}MB)
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Input Area */}
      <div className="p-6 border-t border-slate-200 bg-slate-50">
        <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept=".pdf,.txt,.docx"
            onChange={handleFileSelect}
            className="hidden"
          />

          <Button
            variant="outline"
            size="icon"
            onClick={handleFileUpload}
            className="shrink-0 hover:bg-slate-100 border-slate-300 rounded-xl transition-all duration-200 hover:scale-105 hover:shadow-md"
          >
            <Plus className="w-4 h-4 text-slate-600" />
          </Button>

          <div className="flex-1 relative">
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder={t('typeMessage')}
              className="pr-14 rounded-2xl border-2 border-slate-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-500/20 bg-white shadow-sm text-slate-700 placeholder:text-slate-400"
            />
            <Button
              onClick={handleSendMessage}
              disabled={!inputValue.trim()}
              size="icon"
              className={`absolute ${isRTL ? 'left-2' : 'right-2'} top-1/2 -translate-y-1/2 h-9 w-9 rounded-xl bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 disabled:from-slate-300 disabled:to-slate-400 transition-all duration-200 hover:scale-105 shadow-lg`}
            >
              <Send className={`w-4 h-4 ${isRTL ? 'rotate-180' : ''}`} />
            </Button>
          </div>
        </div>

        {/* Generate Button */}
        {hasUserInput && (
          <div className="mt-8 animate-fade-in">
            <Button
              onClick={handleGenerateBot}
              className="w-full bg-gradient-to-r from-teal-500 via-blue-500 to-purple-600 hover:from-teal-600 hover:via-blue-600 hover:to-purple-700 text-white font-bold py-4 rounded-2xl shadow-2xl hover:shadow-2xl transition-all duration-300 hover:scale-[1.02] text-lg relative overflow-hidden group"
            >
              <span className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
              <span className="relative z-10 flex items-center justify-center gap-2">
                ✨ {t('createChatbot')}
              </span>
            </Button>
          </div>
        )}

        {/* Debug Section - Remove in production */}
        {user && (
          <div className="mt-6 p-4 bg-gray-100 rounded-lg border">
            <h3 className="text-sm font-semibold text-gray-700 mb-2">Debug Tools</h3>
            <div className="flex gap-2 flex-wrap">
              <Button
                onClick={testStorageConnection}
                variant="outline"
                size="sm"
                className="text-xs"
              >
                Test Storage
              </Button>
              <Button
                onClick={() => console.log('Chat History:', chatHistory)}
                variant="outline"
                size="sm"
                className="text-xs"
              >
                Log Chat History
              </Button>
              <Button
                onClick={() => console.log('User:', user)}
                variant="outline"
                size="sm"
                className="text-xs"
              >
                Log User
              </Button>
            </div>
            <div className="mt-2 text-xs text-gray-600">
              Chat History: {chatHistory.length} messages | Files: {uploadedFiles.length} | User: {user?.email}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};