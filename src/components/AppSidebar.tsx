
import { <PERSON><PERSON>, MessageSquare, BarChart3, CreditCard, Settings, TestTube, Plus } from "lucide-react";
import { NavLink } from "react-router-dom";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { useLanguage } from "@/contexts/LanguageContext";

export function AppSidebar() {
  const { state } = useSidebar();
  const { t, isRTL, direction } = useLanguage();
  const isCollapsed = state === "collapsed";

  const items = [
    { title: t('dashboard'), url: "/dashboard", icon: Bot, color: "text-blue-500", bgColor: "bg-blue-50 hover:bg-blue-100" },
    { title: t('createBot'), url: "/create-bot", icon: Plus, color: "text-teal-500", bgColor: "bg-teal-50 hover:bg-teal-100" },
    { title: t('botTesting'), url: "/testing", icon: TestTube, color: "text-purple-500", bgColor: "bg-purple-50 hover:bg-purple-100" },
    { title: t('messages'), url: "/messages", icon: MessageSquare, color: "text-green-500", bgColor: "bg-green-50 hover:bg-green-100" },
    { title: t('analytics'), url: "/analytics", icon: BarChart3, color: "text-orange-500", bgColor: "bg-orange-50 hover:bg-orange-100" },
    { title: t('pricing'), url: "/pricing", icon: CreditCard, color: "text-pink-500", bgColor: "bg-pink-50 hover:bg-pink-100" },
    { title: t('settings'), url: "/settings", icon: Settings, color: "text-gray-500", bgColor: "bg-gray-50 hover:bg-gray-100" },
  ];

  return (
    <Sidebar className={`${isCollapsed ? "w-16" : "w-64"} bg-white border-r border-gray-200 ${isRTL ? 'sidebar-rtl' : ''}`} collapsible="icon" dir={direction}>
      <div className="p-4 border-b bg-gradient-to-r from-teal-500 to-blue-600">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center shadow-lg">
            <Bot className="w-6 h-6 text-white" />
          </div>
          {!isCollapsed && (
            <span className="font-bold text-xl text-white drop-shadow-sm">Chatlink</span>
          )}
        </div>
      </div>

      <SidebarContent className="bg-gray-50 p-2">
        <SidebarGroup>
          <SidebarGroupLabel className={`text-gray-600 text-xs font-semibold px-4 py-3 uppercase tracking-wider ${isRTL ? 'nav-item-rtl' : ''}`}>
            {t('navigation') || 'Navigation'}
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-2 px-2">
              {items.map((item) => (
                <SidebarMenuItem key={item.title} className="w-full">
                  <NavLink
                    to={item.url}
                    className="block w-full"
                  >
                    {({ isActive }) => (
                      <div className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 w-full group ${
                        isActive
                          ? "bg-gradient-to-r from-teal-500 to-blue-600 text-white shadow-lg hover:shadow-xl"
                          : "text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-white hover:shadow-md hover:scale-[1.02]"
                      } ${isRTL ? 'flex-row-reverse-rtl nav-item-rtl' : ''}`}>
                        <div className={`p-2.5 rounded-xl flex-shrink-0 transition-all duration-300 ${
                          isActive
                            ? "bg-white/20 shadow-sm"
                            : `${item.bgColor} group-hover:scale-110`
                        }`}>
                          <item.icon className={`w-5 h-5 transition-colors duration-300 ${
                            isActive ? "text-white" : item.color
                          } ${isRTL ? 'nav-icon-rtl' : ''}`} />
                        </div>
                        {!isCollapsed && (
                          <span className={`font-semibold text-sm flex-1 transition-colors duration-300 ${
                            isActive ? "text-white" : "text-gray-700 group-hover:text-gray-900"
                          }`}>
                            {item.title}
                          </span>
                        )}
                      </div>
                    )}
                  </NavLink>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
