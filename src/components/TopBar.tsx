
import { Bell, User, ChevronDown, CreditCard, HelpCircle, LogOut } from "lucide-react";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";

export const TopBar = () => {
  const { user, signOut } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleSignOut = async () => {
    await signOut();
    toast({
      title: "Signed out",
      description: "You have been successfully signed out",
    });
  };

  const handleProfileSettings = () => {
    navigate('/settings');
  };

  const handleBilling = () => {
    navigate('/pricing');
  };

  const handleHelpSupport = () => {
    toast({
      title: "Help & Support",
      description: "Contact <NAME_EMAIL> for assistance",
    });
  };

  // Get user display name from metadata or email
  const getUserDisplayName = () => {
    if (user?.user_metadata?.first_name && user?.user_metadata?.last_name) {
      return `${user.user_metadata.first_name} ${user.user_metadata.last_name}`;
    }
    return user?.email?.split('@')[0] || 'User';
  };

  return (
    <header className="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-6 shadow-sm">
      <div className="flex items-center gap-4">
        <SidebarTrigger className="p-2 hover:bg-gray-100 rounded-xl transition-all duration-200 hover:scale-105 border border-gray-200 hover:border-gray-300" />
        <div className="hidden md:block">
          <h1 className="text-lg font-semibold text-gray-800">
            Dashboard
          </h1>
        </div>
      </div>

      <div className="flex items-center gap-4">
        {/* Token Balance */}
        <div className="hidden sm:flex items-center gap-2 bg-green-50 px-4 py-2 rounded-lg border border-green-200">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="text-sm font-semibold text-green-700">
            2,450 / 5,000 tokens
          </span>
        </div>

        {/* Notifications */}
        <Button variant="ghost" size="sm" className="relative hover:bg-gray-100 rounded-lg">
          <Bell className="w-5 h-5 text-gray-600" />
          <span className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center font-bold">
            2
          </span>
        </Button>

        {/* User Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="flex items-center gap-3 hover:bg-slate-100 rounded-xl transition-all duration-200 hover:scale-105 px-3 py-2">
              <div className="w-9 h-9 bg-gradient-to-br from-teal-500 via-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg ring-2 ring-white">
                <User className="w-4 h-4 text-white" />
              </div>
              <span className="hidden md:block text-sm font-semibold text-slate-700">{getUserDisplayName()}</span>
              <ChevronDown className="w-4 h-4 text-slate-500 transition-transform duration-200 group-data-[state=open]:rotate-180" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-52 bg-white border border-slate-200 shadow-xl rounded-xl p-2">
            <DropdownMenuItem
              className="cursor-pointer hover:bg-slate-50 rounded-lg px-3 py-2 transition-colors duration-200"
              onClick={handleProfileSettings}
            >
              <User className="w-4 h-4 mr-3 text-slate-500" />
              Profile Settings
            </DropdownMenuItem>
            <DropdownMenuItem
              className="cursor-pointer hover:bg-slate-50 rounded-lg px-3 py-2 transition-colors duration-200"
              onClick={handleBilling}
            >
              <CreditCard className="w-4 h-4 mr-3 text-slate-500" />
              Billing
            </DropdownMenuItem>
            <DropdownMenuItem
              className="cursor-pointer hover:bg-slate-50 rounded-lg px-3 py-2 transition-colors duration-200"
              onClick={handleHelpSupport}
            >
              <HelpCircle className="w-4 h-4 mr-3 text-slate-500" />
              Help & Support
            </DropdownMenuItem>
            <div className="h-px bg-slate-200 my-2"></div>
            <DropdownMenuItem
              className="text-red-600 cursor-pointer hover:bg-red-50 rounded-lg px-3 py-2 transition-colors duration-200"
              onClick={handleSignOut}
            >
              <LogOut className="w-4 h-4 mr-3 text-red-500" />
              Sign Out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
};
