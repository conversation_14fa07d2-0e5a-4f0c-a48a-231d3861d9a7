import React from 'react';
import { Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useLanguage, SUPPORTED_LANGUAGES, LanguageCode } from '@/contexts/LanguageContext';
import { useToast } from '@/hooks/use-toast';

interface LanguageSelectorProps {
  variant?: 'default' | 'compact';
  className?: string;
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({ 
  variant = 'default',
  className = '' 
}) => {
  const { currentLanguage, setLanguage, loading, t } = useLanguage();
  const { toast } = useToast();

  const handleLanguageChange = async (languageCode: LanguageCode) => {
    if (languageCode === currentLanguage) {
      console.log('Language already selected:', languageCode);
      return;
    }

    console.log('User selected language:', languageCode);

    try {
      await setLanguage(languageCode);

      // Show success toast with the new language name
      setTimeout(() => {
        toast({
          title: t('success'),
          description: `${t('languageChanged')} ${SUPPORTED_LANGUAGES[languageCode].name}`,
        });
      }, 100); // Small delay to ensure the language context has updated

    } catch (error) {
      console.error('Language change error:', error);
      toast({
        title: t('error'),
        description: t('failedToChangeLanguage'),
        variant: 'destructive',
      });
    }
  };

  const currentLang = SUPPORTED_LANGUAGES[currentLanguage];

  if (variant === 'compact') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={`flex items-center gap-2 hover:bg-gray-100 rounded-lg border-2 ${
              loading ? 'border-blue-300' : 'border-transparent hover:border-gray-200'
            } ${className}`}
            disabled={loading}
          >
            <span className="text-lg">{currentLang.flag}</span>
            <span className="text-sm font-medium hidden sm:block">
              {loading ? '...' : currentLang.code.toUpperCase()}
            </span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48 bg-white border border-gray-200 shadow-xl rounded-xl p-2">
          {Object.values(SUPPORTED_LANGUAGES).map((language) => {
            const isSelected = currentLanguage === language.code;
            const isChanging = loading && isSelected;

            return (
              <DropdownMenuItem
                key={language.code}
                className={`cursor-pointer hover:bg-gray-50 rounded-lg px-3 py-2 transition-colors duration-200 ${
                  isSelected ? 'bg-blue-50 text-blue-700 border border-blue-200' : ''
                } ${isChanging ? 'opacity-50' : ''}`}
                onClick={() => handleLanguageChange(language.code)}
              >
                <span className="text-lg mr-3">{language.flag}</span>
                <span className="text-sm font-medium">{language.name}</span>
                {isSelected && (
                  <span className="ml-auto text-blue-600 font-bold">
                    {isChanging ? '...' : '✓'}
                  </span>
                )}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          className={`flex items-center gap-3 hover:bg-slate-100 rounded-xl transition-all duration-200 hover:scale-105 px-3 py-2 ${className}`}
          disabled={loading}
        >
          <Globe className="w-4 h-4 text-slate-500" />
          <div className="flex items-center gap-2">
            <span className="text-lg">{currentLang.flag}</span>
            <span className="hidden md:block text-sm font-semibold text-slate-700">
              {currentLang.name}
            </span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-52 bg-white border border-slate-200 shadow-xl rounded-xl p-2">
        <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-100 mb-2">
          {t('selectLanguage')}
        </div>
        {Object.values(SUPPORTED_LANGUAGES).map((language) => {
          const isSelected = currentLanguage === language.code;
          const isChanging = loading && isSelected;

          return (
            <DropdownMenuItem
              key={language.code}
              className={`cursor-pointer hover:bg-slate-50 rounded-lg px-3 py-2 transition-colors duration-200 ${
                isSelected ? 'bg-blue-50 text-blue-700 border border-blue-200' : ''
              } ${isChanging ? 'opacity-50' : ''}`}
              onClick={() => handleLanguageChange(language.code)}
            >
              <span className="text-lg mr-3">{language.flag}</span>
              <span className="text-sm font-medium flex-1">{language.name}</span>
              {isSelected && (
                <span className="text-blue-600 font-bold">
                  {isChanging ? '...' : '✓'}
                </span>
              )}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
