import React from 'react';
import { useLanguage, SUPPORTED_LANGUAGES } from '@/contexts/LanguageContext';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

export const LanguageDebug: React.FC = () => {
  const { currentLanguage, loading, resetLanguage } = useLanguage();
  const { user } = useAuth();

  const localStorageLanguage = localStorage.getItem('preferred_language');
  const userMetadataLanguage = user?.user_metadata?.preferred_language;

  return (
    <Card className="w-full max-w-md mx-auto mt-4 border-2 border-dashed border-gray-300">
      <CardHeader>
        <CardTitle className="text-sm font-mono">Language Debug Info</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2 text-xs font-mono">
        <div className="flex justify-between items-center">
          <span>Current Language:</span>
          <Badge variant="outline" className="font-mono">
            {currentLanguage} ({SUPPORTED_LANGUAGES[currentLanguage].name})
          </Badge>
        </div>
        
        <div className="flex justify-between items-center">
          <span>Loading:</span>
          <Badge variant={loading ? "destructive" : "secondary"}>
            {loading ? 'Yes' : 'No'}
          </Badge>
        </div>
        
        <div className="flex justify-between items-center">
          <span>LocalStorage:</span>
          <Badge variant="outline">
            {localStorageLanguage || 'null'}
          </Badge>
        </div>
        
        <div className="flex justify-between items-center">
          <span>User Metadata:</span>
          <Badge variant="outline">
            {userMetadataLanguage || 'null'}
          </Badge>
        </div>
        
        <div className="flex justify-between items-center">
          <span>User Logged In:</span>
          <Badge variant={user ? "default" : "secondary"}>
            {user ? 'Yes' : 'No'}
          </Badge>
        </div>

        <div className="pt-2 border-t">
          <div className="text-xs text-gray-500 mb-1">Test Translations:</div>
          <div className="space-y-1">
            <div>Dashboard: <span className="text-blue-600">{SUPPORTED_LANGUAGES[currentLanguage].name === 'English' ? 'Dashboard' : 'Translated'}</span></div>
            <div>Settings: <span className="text-blue-600">{SUPPORTED_LANGUAGES[currentLanguage].name === 'English' ? 'Settings' : 'Translated'}</span></div>
          </div>
        </div>

        <div className="pt-2 border-t">
          <Button
            onClick={resetLanguage}
            size="sm"
            variant="outline"
            className="w-full text-xs"
          >
            Reset to English
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
