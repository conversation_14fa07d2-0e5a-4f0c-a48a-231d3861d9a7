/* App Background Styles */
.app-background-wrapper {
  position: relative;
  min-height: 100vh;
  width: 100%;
}

.app-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
  
  /* Main gradient background */
  background: linear-gradient(
    135deg,
    rgba(99, 102, 241, 0.08) 0%,
    rgba(168, 85, 247, 0.06) 25%,
    rgba(236, 72, 153, 0.05) 50%,
    rgba(59, 130, 246, 0.07) 75%,
    rgba(16, 185, 129, 0.05) 100%
  );
  
  /* Subtle texture overlay */
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(168, 85, 247, 0.02) 0%, transparent 50%);
  
  transition: opacity 0.6s ease-in-out;
}

/* Subtle version for focus pages */
.app-background.subtle {
  opacity: 0.4;
}

/* Page-specific themes */
.app-background.landing {
  background: linear-gradient(
    135deg,
    rgba(99, 102, 241, 0.12) 0%,
    rgba(168, 85, 247, 0.10) 25%,
    rgba(236, 72, 153, 0.08) 50%,
    rgba(59, 130, 246, 0.10) 75%,
    rgba(16, 185, 129, 0.08) 100%
  );
}

.app-background.auth {
  background: linear-gradient(
    135deg,
    rgba(168, 85, 247, 0.08) 0%,
    rgba(236, 72, 153, 0.06) 50%,
    rgba(99, 102, 241, 0.08) 100%
  );
}

.app-background.dashboard {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.06) 0%,
    rgba(16, 185, 129, 0.04) 50%,
    rgba(99, 102, 241, 0.05) 100%
  );
}

.app-background.focus {
  background: linear-gradient(
    135deg,
    rgba(99, 102, 241, 0.04) 0%,
    rgba(168, 85, 247, 0.03) 50%,
    rgba(59, 130, 246, 0.04) 100%
  );
}

/* Page transition effects */
.app-background.transitioning {
  transform: scale(1.02);
  transition: transform 0.3s ease-out;
}

.transition-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  pointer-events: none;
}

.transition-overlay.active {
  opacity: 1;
}

/* Floating shapes container */
.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Individual floating shapes */
.shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(99, 102, 241, 0.1), rgba(168, 85, 247, 0.08));
  backdrop-filter: blur(1px);
  animation: float 20s infinite linear;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
  animation-duration: 25s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 20%;
  right: 15%;
  animation-delay: -5s;
  animation-duration: 30s;
  background: linear-gradient(45deg, rgba(236, 72, 153, 0.08), rgba(59, 130, 246, 0.06));
}

.shape-3 {
  width: 60px;
  height: 60px;
  top: 60%;
  left: 20%;
  animation-delay: -10s;
  animation-duration: 22s;
  background: linear-gradient(45deg, rgba(16, 185, 129, 0.09), rgba(99, 102, 241, 0.07));
}

.shape-4 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  right: 25%;
  animation-delay: -15s;
  animation-duration: 28s;
  background: linear-gradient(45deg, rgba(168, 85, 247, 0.07), rgba(236, 72, 153, 0.05));
}

.shape-5 {
  width: 140px;
  height: 140px;
  top: 40%;
  left: 60%;
  animation-delay: -8s;
  animation-duration: 35s;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.06), rgba(16, 185, 129, 0.04));
}

.shape-6 {
  width: 70px;
  height: 70px;
  bottom: 40%;
  left: 80%;
  animation-delay: -12s;
  animation-duration: 26s;
  background: linear-gradient(45deg, rgba(99, 102, 241, 0.08), rgba(168, 85, 247, 0.06));
}

/* Floating animation */
@keyframes float {
  0% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-20px) translateX(10px) rotate(90deg);
    opacity: 0.9;
  }
  50% {
    transform: translateY(-10px) translateX(-15px) rotate(180deg);
    opacity: 0.6;
  }
  75% {
    transform: translateY(-30px) translateX(5px) rotate(270deg);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0px) translateX(0px) rotate(360deg);
    opacity: 0.7;
  }
}

/* Wave overlay */
.wave-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 200px;
  overflow: hidden;
  opacity: 0.3;
}

.wave {
  position: relative;
  width: 100%;
  height: 100%;
  animation: wave-movement 15s ease-in-out infinite;
}

.shape-fill {
  fill: rgba(99, 102, 241, 0.1);
}

@keyframes wave-movement {
  0%, 100% {
    transform: translateX(0px);
  }
  50% {
    transform: translateX(-20px);
  }
}

/* Content wrapper */
.app-content {
  position: relative;
  z-index: 1;
  min-height: 100vh;
  backdrop-filter: blur(0.5px);
}

/* Enhanced card backgrounds for better readability */
.app-content .bg-white,
.app-content .bg-gray-50,
.app-content .bg-card {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Enhanced sidebar and navigation */
.app-content nav,
.app-content aside {
  background-color: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(15px);
}

/* Subtle glow effect for interactive elements */
.app-content button:hover,
.app-content .hover\\:bg-accent:hover {
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.1);
  transition: box-shadow 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .shape {
    transform: scale(0.7);
  }
  
  .shape-1, .shape-3, .shape-6 {
    display: none; /* Hide some shapes on mobile for performance */
  }
  
  .wave-overlay {
    height: 120px;
  }
}

@media (max-width: 480px) {
  .app-background {
    background: linear-gradient(
      135deg,
      rgba(99, 102, 241, 0.05) 0%,
      rgba(168, 85, 247, 0.04) 50%,
      rgba(59, 130, 246, 0.05) 100%
    );
  }
  
  .shape {
    transform: scale(0.5);
  }
  
  .shape-2, .shape-4 {
    display: none; /* Hide more shapes on small mobile */
  }
}

/* RTL support */
[dir="rtl"] .floating-shapes {
  transform: scaleX(-1);
}

[dir="rtl"] .wave-overlay {
  transform: scaleX(-1);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .shape {
    animation: none;
  }
  
  .wave {
    animation: none;
  }
  
  .app-background {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .app-background {
    background: rgba(248, 250, 252, 0.95);
  }
  
  .shape {
    display: none;
  }
  
  .wave-overlay {
    display: none;
  }
}

/* Dark mode adjustments (if needed) */
@media (prefers-color-scheme: dark) {
  .app-background {
    background: linear-gradient(
      135deg,
      rgba(30, 41, 59, 0.8) 0%,
      rgba(51, 65, 85, 0.7) 25%,
      rgba(71, 85, 105, 0.6) 50%,
      rgba(51, 65, 85, 0.7) 75%,
      rgba(30, 41, 59, 0.8) 100%
    );
  }

  .shape-fill {
    fill: rgba(148, 163, 184, 0.1);
  }

  .app-content .bg-white,
  .app-content .bg-gray-50,
  .app-content .bg-card {
    background-color: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(71, 85, 105, 0.3);
  }
}

/* Performance optimizations */
.app-background,
.floating-shapes,
.shape,
.wave-overlay {
  will-change: transform;
  transform: translateZ(0);
}

/* Smooth scrolling for better UX */
.app-content {
  scroll-behavior: smooth;
}

/* Focus indicators that work with the background */
.app-content *:focus-visible {
  outline: 2px solid rgba(99, 102, 241, 0.6);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Loading state background */
.app-background.loading {
  animation: pulse-background 2s ease-in-out infinite;
}

@keyframes pulse-background {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}
