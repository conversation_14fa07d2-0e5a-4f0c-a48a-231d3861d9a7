/* RTL (Right-to-Left) Support Styles */

/* Base RTL styles */
[dir="rtl"] {
  text-align: right;
}

[dir="ltr"] {
  text-align: left;
}

/* Flexbox direction adjustments */
[dir="rtl"] .flex-row-reverse-rtl {
  flex-direction: row-reverse;
}

[dir="rtl"] .justify-start-rtl {
  justify-content: flex-end;
}

[dir="rtl"] .justify-end-rtl {
  justify-content: flex-start;
}

/* Margin and padding adjustments */
[dir="rtl"] .ml-auto-rtl {
  margin-right: auto;
  margin-left: unset;
}

[dir="rtl"] .mr-auto-rtl {
  margin-left: auto;
  margin-right: unset;
}

[dir="rtl"] .pl-rtl {
  padding-right: var(--padding-value);
  padding-left: unset;
}

[dir="rtl"] .pr-rtl {
  padding-left: var(--padding-value);
  padding-right: unset;
}

/* Border radius adjustments */
[dir="rtl"] .rounded-l-rtl {
  border-top-right-radius: var(--radius);
  border-bottom-right-radius: var(--radius);
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

[dir="rtl"] .rounded-r-rtl {
  border-top-left-radius: var(--radius);
  border-bottom-left-radius: var(--radius);
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

/* Transform adjustments for icons */
[dir="rtl"] .transform-rtl {
  transform: scaleX(-1);
}

/* Sidebar adjustments */
[dir="rtl"] .sidebar-rtl {
  right: 0;
  left: unset;
}

[dir="rtl"] .sidebar-content-rtl {
  padding-right: 1rem;
  padding-left: 0.5rem;
}

/* Dropdown menu adjustments */
[dir="rtl"] .dropdown-menu-rtl {
  right: 0;
  left: unset;
}

/* Navigation adjustments */
[dir="rtl"] .nav-item-rtl {
  text-align: right;
}

[dir="rtl"] .nav-icon-rtl {
  margin-left: 0.75rem;
  margin-right: 0;
}

/* Form adjustments */
[dir="rtl"] .form-label-rtl {
  text-align: right;
}

[dir="rtl"] .form-input-rtl {
  text-align: right;
}

/* Button icon adjustments */
[dir="rtl"] .btn-icon-left-rtl {
  margin-left: 0.5rem;
  margin-right: 0;
}

[dir="rtl"] .btn-icon-right-rtl {
  margin-right: 0.5rem;
  margin-left: 0;
}

/* Card adjustments */
[dir="rtl"] .card-header-rtl {
  text-align: right;
}

/* Toast/notification adjustments */
[dir="rtl"] .toast-rtl {
  right: 1rem;
  left: unset;
}

/* Specific component adjustments */
[dir="rtl"] .language-selector-rtl .flag {
  margin-left: 0.75rem;
  margin-right: 0;
}

[dir="rtl"] .topbar-rtl {
  flex-direction: row-reverse;
}

[dir="rtl"] .topbar-title-rtl {
  text-align: right;
}

/* Chat interface adjustments */
[dir="rtl"] .chat-message-user {
  text-align: right;
  margin-left: 2rem;
  margin-right: 0;
}

[dir="rtl"] .chat-message-bot {
  text-align: right;
  margin-right: 2rem;
  margin-left: 0;
}

/* Dashboard grid adjustments */
[dir="rtl"] .dashboard-grid-rtl {
  direction: rtl;
}

[dir="rtl"] .dashboard-card-rtl {
  text-align: right;
}

/* Landing page hero adjustments */
[dir="rtl"] .hero-content-rtl {
  text-align: right;
}

[dir="rtl"] .hero-buttons-rtl {
  justify-content: flex-end;
}

/* Utility classes for common RTL patterns */
.rtl-flip {
  transform: scaleX(-1);
}

[dir="rtl"] .rtl-flip {
  transform: scaleX(1);
}

.ltr-flip {
  transform: scaleX(1);
}

[dir="rtl"] .ltr-flip {
  transform: scaleX(-1);
}

/* Animation adjustments */
[dir="rtl"] .slide-in-right {
  animation: slideInLeft 0.3s ease-out;
}

[dir="rtl"] .slide-in-left {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Scrollbar adjustments for RTL */
[dir="rtl"] .scrollbar-rtl::-webkit-scrollbar {
  width: 6px;
}

[dir="rtl"] .scrollbar-rtl::-webkit-scrollbar-track {
  background: #f1f1f1;
}

[dir="rtl"] .scrollbar-rtl::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

/* Table adjustments */
[dir="rtl"] .table-rtl th,
[dir="rtl"] .table-rtl td {
  text-align: right;
}

[dir="rtl"] .table-rtl th:first-child,
[dir="rtl"] .table-rtl td:first-child {
  padding-right: 1rem;
  padding-left: 0.5rem;
}

/* Modal adjustments */
[dir="rtl"] .modal-rtl {
  text-align: right;
}

[dir="rtl"] .modal-header-rtl {
  flex-direction: row-reverse;
}

/* Breadcrumb adjustments */
[dir="rtl"] .breadcrumb-rtl {
  flex-direction: row-reverse;
}

[dir="rtl"] .breadcrumb-separator-rtl::before {
  content: "\\";
  transform: scaleX(-1);
}
