export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      bots: {
        Row: {
          channel: Database["public"]["Enums"]["bot_channel"]
          created_at: string
          embedding_context_id: string | null
          id: string
          name: string
          status: Database["public"]["Enums"]["bot_status"]
          updated_at: string
          user_id: string
        }
        Insert: {
          channel: Database["public"]["Enums"]["bot_channel"]
          created_at?: string
          embedding_context_id?: string | null
          id?: string
          name: string
          status?: Database["public"]["Enums"]["bot_status"]
          updated_at?: string
          user_id: string
        }
        Update: {
          channel?: Database["public"]["Enums"]["bot_channel"]
          created_at?: string
          embedding_context_id?: string | null
          id?: string
          name?: string
          status?: Database["public"]["Enums"]["bot_status"]
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "bots_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      documents: {
        Row: {
          bot_id: string | null
          content: string | null
          created_at: string
          embedding_status: Database["public"]["Enums"]["embedding_status"]
          file_name: string
          file_url: string | null
          id: string
          type: Database["public"]["Enums"]["document_type"]
          user_id: string
        }
        Insert: {
          bot_id?: string | null
          content?: string | null
          created_at?: string
          embedding_status?: Database["public"]["Enums"]["embedding_status"]
          file_name: string
          file_url?: string | null
          id?: string
          type: Database["public"]["Enums"]["document_type"]
          user_id: string
        }
        Update: {
          bot_id?: string | null
          content?: string | null
          created_at?: string
          embedding_status?: Database["public"]["Enums"]["embedding_status"]
          file_name?: string
          file_url?: string | null
          id?: string
          type?: Database["public"]["Enums"]["document_type"]
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "documents_bot_id_fkey"
            columns: ["bot_id"]
            isOneToOne: false
            referencedRelation: "bots"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "documents_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      messages: {
        Row: {
          bot_id: string
          channel: Database["public"]["Enums"]["bot_channel"]
          content: string
          created_at: string
          id: string
          recipient_number: string | null
          sender: Database["public"]["Enums"]["message_sender"]
          timestamp: string
        }
        Insert: {
          bot_id: string
          channel: Database["public"]["Enums"]["bot_channel"]
          content: string
          created_at?: string
          id?: string
          recipient_number?: string | null
          sender: Database["public"]["Enums"]["message_sender"]
          timestamp?: string
        }
        Update: {
          bot_id?: string
          channel?: Database["public"]["Enums"]["bot_channel"]
          content?: string
          created_at?: string
          id?: string
          recipient_number?: string | null
          sender?: Database["public"]["Enums"]["message_sender"]
          timestamp?: string
        }
        Relationships: [
          {
            foreignKeyName: "messages_bot_id_fkey"
            columns: ["bot_id"]
            isOneToOne: false
            referencedRelation: "bots"
            referencedColumns: ["id"]
          },
        ]
      }
      plans: {
        Row: {
          created_at: string
          id: string
          monthly_token_limit: number
          name: string
          price_usd: number
        }
        Insert: {
          created_at?: string
          id?: string
          monthly_token_limit: number
          name: string
          price_usd?: number
        }
        Update: {
          created_at?: string
          id?: string
          monthly_token_limit?: number
          name?: string
          price_usd?: number
        }
        Relationships: []
      }
      profiles: {
        Row: {
          created_at: string | null
          email: string | null
          first_name: string | null
          full_name: string | null
          id: string
          last_name: string | null
          phone_number: string | null
          plan: Database["public"]["Enums"]["plan_type"] | null
          tokens_used: number | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          email?: string | null
          first_name?: string | null
          full_name?: string | null
          id: string
          last_name?: string | null
          phone_number?: string | null
          plan?: Database["public"]["Enums"]["plan_type"] | null
          tokens_used?: number | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string | null
          first_name?: string | null
          full_name?: string | null
          id?: string
          last_name?: string | null
          phone_number?: string | null
          plan?: Database["public"]["Enums"]["plan_type"] | null
          tokens_used?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      token_usage: {
        Row: {
          created_at: string
          description: string | null
          id: string
          source: Database["public"]["Enums"]["token_source"]
          tokens_used: number
          user_id: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          source: Database["public"]["Enums"]["token_source"]
          tokens_used: number
          user_id: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          source?: Database["public"]["Enums"]["token_source"]
          tokens_used?: number
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "token_usage_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      track_token_usage: {
        Args: {
          p_user_id: string
          p_tokens: number
          p_description: string
          p_source: Database["public"]["Enums"]["token_source"]
        }
        Returns: undefined
      }
    }
    Enums: {
      bot_channel: "whatsapp" | "sms" | "messenger"
      bot_status: "active" | "paused" | "archived"
      document_type: "pdf" | "txt" | "docx" | "chat"
      embedding_status: "pending" | "processed" | "failed"
      message_sender: "user" | "bot"
      plan_type: "free" | "pro" | "business"
      token_source: "generation" | "upload" | "chat" | "integration"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      bot_channel: ["whatsapp", "sms", "messenger"],
      bot_status: ["active", "paused", "archived"],
      document_type: ["pdf", "txt", "docx", "chat"],
      embedding_status: ["pending", "processed", "failed"],
      message_sender: ["user", "bot"],
      plan_type: ["free", "pro", "business"],
      token_source: ["generation", "upload", "chat", "integration"],
    },
  },
} as const
