// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://mzfbtqeyhfeddihlywjz.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im16ZmJ0cWV5aGZlZGRpaGx5d2p6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMTI4NTEsImV4cCI6MjA2Njg4ODg1MX0.C8bFcH8Zf89Pxg4FY5zqBSS0bfmuAsT3wcFtbHDKp8A";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});