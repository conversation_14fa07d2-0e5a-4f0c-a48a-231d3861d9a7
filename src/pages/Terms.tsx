import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft } from "lucide-react";

const Terms = () => {
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <Link
            to="/signup"
            className="inline-flex items-center gap-2 text-sm text-teal-600 hover:text-teal-500 mb-4"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Sign Up
          </Link>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-3xl font-bold">Terms of Service</CardTitle>
            <p className="text-gray-600">Last updated: {new Date().toLocaleDateString()}</p>
          </CardHeader>
          <CardContent className="prose max-w-none">
            <div className="space-y-6">
              <section>
                <h2 className="text-xl font-semibold mb-3">1. Acceptance of Terms</h2>
                <p className="text-gray-700 leading-relaxed">
                  By accessing and using Chatlink's services, you accept and agree to be bound by the terms and provision of this agreement.
                </p>
              </section>

              <section>
                <h2 className="text-xl font-semibold mb-3">2. Service Description</h2>
                <p className="text-gray-700 leading-relaxed">
                  Chatlink provides a platform for creating and deploying chatbots for various messaging platforms including WhatsApp, Facebook Messenger, and SMS.
                </p>
              </section>

              <section>
                <h2 className="text-xl font-semibold mb-3">3. User Responsibilities</h2>
                <ul className="list-disc pl-6 text-gray-700 space-y-2">
                  <li>You are responsible for maintaining the confidentiality of your account</li>
                  <li>You agree to use the service in compliance with all applicable laws</li>
                  <li>You will not use the service for any unlawful or prohibited activities</li>
                  <li>You are responsible for all content uploaded to your chatbots</li>
                </ul>
              </section>

              <section>
                <h2 className="text-xl font-semibold mb-3">4. Privacy and Data</h2>
                <p className="text-gray-700 leading-relaxed">
                  Your privacy is important to us. Please review our Privacy Policy to understand how we collect, use, and protect your information.
                </p>
              </section>

              <section>
                <h2 className="text-xl font-semibold mb-3">5. Service Availability</h2>
                <p className="text-gray-700 leading-relaxed">
                  We strive to maintain high service availability but cannot guarantee uninterrupted service. We reserve the right to modify or discontinue the service with notice.
                </p>
              </section>

              <section>
                <h2 className="text-xl font-semibold mb-3">6. Limitation of Liability</h2>
                <p className="text-gray-700 leading-relaxed">
                  Chatlink shall not be liable for any indirect, incidental, special, consequential, or punitive damages resulting from your use of the service.
                </p>
              </section>

              <section>
                <h2 className="text-xl font-semibold mb-3">7. Changes to Terms</h2>
                <p className="text-gray-700 leading-relaxed">
                  We reserve the right to modify these terms at any time. Users will be notified of significant changes via email or through the platform.
                </p>
              </section>

              <section>
                <h2 className="text-xl font-semibold mb-3">8. Contact Information</h2>
                <p className="text-gray-700 leading-relaxed">
                  If you have any questions about these Terms of Service, please contact <NAME_EMAIL>.
                </p>
              </section>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Terms;
