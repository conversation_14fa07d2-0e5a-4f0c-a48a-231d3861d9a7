
import { <PERSON>, <PERSON><PERSON>, <PERSON>, Crown } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const Pricing = () => {
  const plans = [
    {
      name: "Free",
      price: "$0",
      period: "forever",
      description: "Perfect for trying out Chatabot",
      tokens: "500",
      features: [
        "1 chatbot",
        "500 messages/month",
        "WhatsApp integration",
        "Basic analytics",
        "Community support"
      ],
      limitations: [
        "Limited file uploads (2 files)",
        "Basic customization"
      ],
      icon: Zap,
      color: "border-gray-200",
      buttonColor: "bg-gray-100 text-gray-800 hover:bg-gray-200",
      current: false
    },
    {
      name: "Pro",
      price: "$29",
      period: "per month",
      description: "Best for growing businesses",
      tokens: "5,000",
      features: [
        "5 chatbots",
        "5,000 messages/month",
        "All platform integrations",
        "Advanced analytics",
        "Priority support",
        "Custom branding",
        "API access"
      ],
      limitations: [],
      icon: Star,
      color: "border-teal-500 ring-2 ring-teal-100",
      buttonColor: "bg-teal-500 text-white hover:bg-teal-600",
      popular: true,
      current: true
    },
    {
      name: "Business",
      price: "$99",
      period: "per month",
      description: "For established businesses",
      tokens: "20,000",
      features: [
        "Unlimited chatbots",
        "20,000 messages/month",
        "All platform integrations",
        "Advanced analytics & reports",
        "24/7 phone support",
        "White-label solution",
        "Custom integrations",
        "Dedicated account manager"
      ],
      limitations: [],
      icon: Crown,
      color: "border-purple-200",
      buttonColor: "bg-purple-500 text-white hover:bg-purple-600",
      current: false
    }
  ];

  return (
    <div className="max-w-6xl mx-auto space-y-12">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gray-900">
          Choose Your Plan
        </h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Start free and scale as you grow. All plans include our core features with different usage limits.
        </p>
      </div>

      {/* Current Usage */}
      <Card className="bg-gradient-to-r from-teal-50 to-blue-50 border-teal-200">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            <div>
              <h3 className="font-semibold text-gray-900">Current Usage</h3>
              <p className="text-gray-600">You're on the Pro plan</p>
            </div>
            <div className="flex items-center gap-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-teal-600">2,450</p>
                <p className="text-sm text-gray-600">Tokens Used</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">2,550</p>
                <p className="text-sm text-gray-600">Remaining</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-purple-600">2</p>
                <p className="text-sm text-gray-600">Active Bots</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Pricing Cards */}
      <div className="grid md:grid-cols-3 gap-8">
        {plans.map((plan, index) => (
          <Card key={index} className={`relative ${plan.color} hover:shadow-lg transition-all duration-200`}>
            {plan.popular && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-teal-500 text-white px-4 py-1">Most Popular</Badge>
              </div>
            )}
            {plan.current && (
              <div className="absolute -top-3 right-4">
                <Badge className="bg-blue-500 text-white px-3 py-1">Current Plan</Badge>
              </div>
            )}
            
            <CardHeader className="text-center pb-4">
              <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <plan.icon className="w-8 h-8 text-gray-600" />
              </div>
              <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
              <CardDescription className="text-gray-600">{plan.description}</CardDescription>
              <div className="mt-4">
                <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                <span className="text-gray-600 ml-2">/{plan.period}</span>
              </div>
              <p className="text-sm text-gray-500 mt-2">{plan.tokens} tokens included</p>
            </CardHeader>

            <CardContent className="space-y-6">
              <div className="space-y-3">
                {plan.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>

              {plan.limitations.length > 0 && (
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-800">Limitations:</p>
                  {plan.limitations.map((limitation, limitIndex) => (
                    <p key={limitIndex} className="text-sm text-gray-600">• {limitation}</p>
                  ))}
                </div>
              )}

              <Button 
                className={`w-full ${plan.buttonColor} font-semibold py-3`}
                disabled={plan.current}
              >
                {plan.current ? "Current Plan" : `Choose ${plan.name}`}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* FAQ Section */}
      <div className="bg-gray-50 rounded-2xl p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
          Frequently Asked Questions
        </h2>
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">What are tokens?</h3>
            <p className="text-gray-600 text-sm">
              Tokens are used to measure your chatbot's usage. Each message sent or received by your bot consumes tokens based on length and complexity.
            </p>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">Can I change plans anytime?</h3>
            <p className="text-gray-600 text-sm">
              Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately and are prorated.
            </p>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">What happens if I exceed my token limit?</h3>
            <p className="text-gray-600 text-sm">
              Your bots will temporarily pause until the next billing cycle or you can purchase additional token packages.
            </p>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">Is there a setup fee?</h3>
            <p className="text-gray-600 text-sm">
              No setup fees! All plans include everything you need to get started immediately.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Pricing;
