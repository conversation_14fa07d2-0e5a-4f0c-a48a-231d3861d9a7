
import { Check, Zap, Star, Crown } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useLanguage } from "@/contexts/LanguageContext";

const Pricing = () => {
  const { t, isRTL, direction } = useLanguage();

  // FAQ translations
  const faqTranslations = {
    en: {
      frequentlyAskedQuestions: 'Frequently Asked Questions',
      whatAreTokens: 'What are tokens?',
      tokensExplanation: 'Tokens are used to measure your chatbot\'s usage. Each message sent or received by your bot consumes tokens based on length and complexity.',
      canIChangePlans: 'Can I change plans anytime?',
      changePlansAnswer: 'Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately and are prorated.',
      whatIfExceedLimit: 'What happens if I exceed my token limit?',
      exceedLimitAnswer: 'Your bots will temporarily pause until the next billing cycle or you can purchase additional token packages.',
      isThereSetupFee: 'Is there a setup fee?',
      setupFeeAnswer: 'No setup fees! All plans include everything you need to get started immediately.'
    },
    es: {
      frequentlyAskedQuestions: 'Preguntas Frecuentes',
      whatAreTokens: '¿Qué son los tokens?',
      tokensExplanation: 'Los tokens se usan para medir el uso de tu chatbot. Cada mensaje enviado o recibido por tu bot consume tokens según la longitud y complejidad.',
      canIChangePlans: '¿Puedo cambiar de plan en cualquier momento?',
      changePlansAnswer: '¡Sí! Puedes actualizar o degradar tu plan en cualquier momento. Los cambios toman efecto inmediatamente y se prorratean.',
      whatIfExceedLimit: '¿Qué pasa si excedo mi límite de tokens?',
      exceedLimitAnswer: 'Tus bots se pausarán temporalmente hasta el próximo ciclo de facturación o puedes comprar paquetes adicionales de tokens.',
      isThereSetupFee: '¿Hay una tarifa de configuración?',
      setupFeeAnswer: '¡Sin tarifas de configuración! Todos los planes incluyen todo lo que necesitas para comenzar inmediatamente.'
    },
    ar: {
      frequentlyAskedQuestions: 'الأسئلة الشائعة',
      whatAreTokens: 'ما هي الرموز المميزة؟',
      tokensExplanation: 'تُستخدم الرموز المميزة لقياس استخدام الشات بوت الخاص بك. كل رسالة يتم إرسالها أو استقبالها بواسطة البوت تستهلك رموز مميزة حسب الطول والتعقيد.',
      canIChangePlans: 'هل يمكنني تغيير الخطط في أي وقت؟',
      changePlansAnswer: 'نعم! يمكنك ترقية أو تخفيض خطتك في أي وقت. التغييرات تسري فوراً ويتم احتسابها بالتناسب.',
      whatIfExceedLimit: 'ماذا يحدث إذا تجاوزت حد الرموز المميزة؟',
      exceedLimitAnswer: 'ستتوقف البوتات مؤقتاً حتى دورة الفوترة التالية أو يمكنك شراء حزم رموز إضافية.',
      isThereSetupFee: 'هل هناك رسوم إعداد؟',
      setupFeeAnswer: 'لا توجد رسوم إعداد! جميع الخطط تشمل كل ما تحتاجه للبدء فوراً.'
    },
    fr: {
      frequentlyAskedQuestions: 'Questions Fréquemment Posées',
      whatAreTokens: 'Que sont les jetons ?',
      tokensExplanation: 'Les jetons sont utilisés pour mesurer l\'utilisation de votre chatbot. Chaque message envoyé ou reçu par votre bot consomme des jetons selon la longueur et la complexité.',
      canIChangePlans: 'Puis-je changer de plan à tout moment ?',
      changePlansAnswer: 'Oui ! Vous pouvez mettre à niveau ou rétrograder votre plan à tout moment. Les changements prennent effet immédiatement et sont au prorata.',
      whatIfExceedLimit: 'Que se passe-t-il si je dépasse ma limite de jetons ?',
      exceedLimitAnswer: 'Vos bots se mettront temporairement en pause jusqu\'au prochain cycle de facturation ou vous pouvez acheter des packages de jetons supplémentaires.',
      isThereSetupFee: 'Y a-t-il des frais d\'installation ?',
      setupFeeAnswer: 'Aucuns frais d\'installation ! Tous les plans incluent tout ce dont vous avez besoin pour commencer immédiatement.'
    },
    de: {
      frequentlyAskedQuestions: 'Häufig Gestellte Fragen',
      whatAreTokens: 'Was sind Token?',
      tokensExplanation: 'Token werden verwendet, um die Nutzung Ihres Chatbots zu messen. Jede von Ihrem Bot gesendete oder empfangene Nachricht verbraucht Token basierend auf Länge und Komplexität.',
      canIChangePlans: 'Kann ich jederzeit den Plan wechseln?',
      changePlansAnswer: 'Ja! Sie können Ihren Plan jederzeit upgraden oder downgraden. Änderungen werden sofort wirksam und anteilig berechnet.',
      whatIfExceedLimit: 'Was passiert, wenn ich mein Token-Limit überschreite?',
      exceedLimitAnswer: 'Ihre Bots werden vorübergehend pausiert bis zum nächsten Abrechnungszyklus oder Sie können zusätzliche Token-Pakete kaufen.',
      isThereSetupFee: 'Gibt es eine Einrichtungsgebühr?',
      setupFeeAnswer: 'Keine Einrichtungsgebühren! Alle Pläne enthalten alles, was Sie brauchen, um sofort zu starten.'
    },
    zh: {
      frequentlyAskedQuestions: '常见问题',
      whatAreTokens: '什么是令牌？',
      tokensExplanation: '令牌用于衡量您的聊天机器人的使用情况。您的机器人发送或接收的每条消息都会根据长度和复杂性消耗令牌。',
      canIChangePlans: '我可以随时更改计划吗？',
      changePlansAnswer: '是的！您可以随时升级或降级您的计划。更改立即生效并按比例计费。',
      whatIfExceedLimit: '如果我超过令牌限制会怎样？',
      exceedLimitAnswer: '您的机器人将暂时暂停，直到下一个计费周期，或者您可以购买额外的令牌包。',
      isThereSetupFee: '有设置费吗？',
      setupFeeAnswer: '没有设置费！所有计划都包含您立即开始所需的一切。'
    },
    ja: {
      frequentlyAskedQuestions: 'よくある質問',
      whatAreTokens: 'トークンとは何ですか？',
      tokensExplanation: 'トークンはチャットボットの使用量を測定するために使用されます。ボットが送信または受信する各メッセージは、長さと複雑さに基づいてトークンを消費します。',
      canIChangePlans: 'いつでもプランを変更できますか？',
      changePlansAnswer: 'はい！いつでもプランをアップグレードまたはダウングレードできます。変更は即座に有効になり、日割り計算されます。',
      whatIfExceedLimit: 'トークン制限を超えるとどうなりますか？',
      exceedLimitAnswer: 'ボットは次の請求サイクルまで一時的に停止するか、追加のトークンパッケージを購入できます。',
      isThereSetupFee: 'セットアップ料金はありますか？',
      setupFeeAnswer: 'セットアップ料金はありません！すべてのプランには、すぐに始めるために必要なものがすべて含まれています。'
    },
    ko: {
      frequentlyAskedQuestions: '자주 묻는 질문',
      whatAreTokens: '토큰이란 무엇인가요？',
      tokensExplanation: '토큰은 챗봇의 사용량을 측정하는 데 사용됩니다. 봇이 보내거나 받는 각 메시지는 길이와 복잡성에 따라 토큰을 소비합니다.',
      canIChangePlans: '언제든지 플랜을 변경할 수 있나요？',
      changePlansAnswer: '네! 언제든지 플랜을 업그레이드하거나 다운그레이드할 수 있습니다. 변경사항은 즉시 적용되며 비례 계산됩니다.',
      whatIfExceedLimit: '토큰 한도를 초과하면 어떻게 되나요？',
      exceedLimitAnswer: '봇은 다음 청구 주기까지 일시적으로 일시 중지되거나 추가 토큰 패키지를 구매할 수 있습니다.',
      isThereSetupFee: '설정 수수료가 있나요？',
      setupFeeAnswer: '설정 수수료가 없습니다! 모든 플랜에는 즉시 시작하는 데 필요한 모든 것이 포함되어 있습니다.'
    },
    it: {
      frequentlyAskedQuestions: 'Domande Frequenti',
      whatAreTokens: 'Cosa sono i token?',
      tokensExplanation: 'I token sono usati per misurare l\'utilizzo del tuo chatbot. Ogni messaggio inviato o ricevuto dal tuo bot consume token basati su lunghezza e complessità.',
      canIChangePlans: 'Posso cambiare piano in qualsiasi momento?',
      changePlansAnswer: 'Sì! Puoi aggiornare o declassare il tuo piano in qualsiasi momento. I cambiamenti hanno effetto immediatamente e sono proporzionali.',
      whatIfExceedLimit: 'Cosa succede se supero il limite di token?',
      exceedLimitAnswer: 'I tuoi bot si metteranno temporaneamente in pausa fino al prossimo ciclo di fatturazione o puoi acquistare pacchetti di token aggiuntivi.',
      isThereSetupFee: 'C\'è una tassa di configurazione?',
      setupFeeAnswer: 'Nessuna tassa di configurazione! Tutti i piani includono tutto ciò di cui hai bisogno per iniziare immediatamente.'
    },
    pt: {
      frequentlyAskedQuestions: 'Perguntas Frequentes',
      whatAreTokens: 'O que são tokens?',
      tokensExplanation: 'Tokens são usados para medir o uso do seu chatbot. Cada mensagem enviada ou recebida pelo seu bot consome tokens baseados no comprimento e complexidade.',
      canIChangePlans: 'Posso mudar de plano a qualquer momento?',
      changePlansAnswer: 'Sim! Você pode fazer upgrade ou downgrade do seu plano a qualquer momento. As mudanças têm efeito imediato e são proporcionais.',
      whatIfExceedLimit: 'O que acontece se eu exceder meu limite de tokens?',
      exceedLimitAnswer: 'Seus bots pausarão temporariamente até o próximo ciclo de cobrança ou você pode comprar pacotes de tokens adicionais.',
      isThereSetupFee: 'Há uma taxa de configuração?',
      setupFeeAnswer: 'Sem taxas de configuração! Todos os planos incluem tudo que você precisa para começar imediatamente.'
    }
  };

  const getCurrentLanguage = () => {
    const currentLang = localStorage.getItem('language') || 'en';
    return faqTranslations[currentLang as keyof typeof faqTranslations] || faqTranslations.en;
  };

  const faq = getCurrentLanguage();
  const plans = [
    {
      name: t('free'),
      price: "$0",
      period: t('forever'),
      description: t('perfectForTrying'),
      tokens: "500",
      features: [
        `1 ${t('chatbot')}`,
        `500 ${t('messagesMonth')}`,
        t('whatsappIntegration'),
        t('basicAnalytics'),
        t('communitySupport')
      ],
      limitations: [
        t('limitedFileUploads'),
        t('basicCustomization')
      ],
      icon: Zap,
      color: "border-gray-200",
      buttonColor: "bg-gray-100 text-gray-800 hover:bg-gray-200",
      buttonText: t('getStartedFree'),
      current: false
    },
    {
      name: t('pro'),
      price: "$29",
      period: t('perMonth'),
      description: t('bestForGrowing'),
      tokens: "5,000",
      features: [
        `5 ${t('chatbots')}`,
        `5,000 ${t('messagesMonth')}`,
        t('allPlatformIntegrations'),
        t('advancedAnalytics'),
        t('prioritySupport'),
        t('customBranding'),
        t('apiAccess')
      ],
      limitations: [],
      icon: Star,
      color: "border-teal-500 ring-2 ring-teal-100",
      buttonColor: "bg-teal-500 text-white hover:bg-teal-600",
      buttonText: t('upgradeToPro'),
      popular: true,
      current: true
    },
    {
      name: t('enterprise'),
      price: "$99",
      period: t('perMonth'),
      description: t('forLargeOrganizations'),
      tokens: "20,000",
      features: [
        t('unlimitedChatbots'),
        `20,000 ${t('messagesMonth')}`,
        t('allPlatformIntegrations'),
        t('advancedAnalytics'),
        t('dedicatedSupport'),
        t('customBranding'),
        t('customIntegrations'),
        t('slaGuarantee')
      ],
      limitations: [],
      icon: Crown,
      color: "border-purple-200",
      buttonColor: "bg-purple-500 text-white hover:bg-purple-600",
      buttonText: t('contactSales'),
      current: false
    }
  ];

  return (
    <div className="space-y-12" dir={direction}>
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gray-900">
          {t('choosePlan')}
        </h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          {t('selectPerfectPlan')}
        </p>
      </div>

      {/* Current Usage */}
      <Card className="bg-gradient-to-r from-teal-50 to-blue-50 border-teal-200">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            <div>
              <h3 className="font-semibold text-gray-900">{t('usageThisMonth')}</h3>
              <p className="text-gray-600">{t('currentPlanFree')}</p>
            </div>
            <div className="flex items-center gap-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-teal-600">2,450</p>
                <p className="text-sm text-gray-600">{t('messagesUsed')}</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">2,550</p>
                <p className="text-sm text-gray-600">Remaining</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-purple-600">2</p>
                <p className="text-sm text-gray-600">Active Bots</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Pricing Cards */}
      <div className="grid md:grid-cols-3 gap-8">
        {plans.map((plan, index) => (
          <Card key={index} className={`relative ${plan.color} hover:shadow-lg transition-all duration-200`}>
            {plan.popular && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-teal-500 text-white px-4 py-1">{t('mostPopular')}</Badge>
              </div>
            )}
            {plan.current && (
              <div className="absolute -top-3 right-4">
                <Badge className="bg-blue-500 text-white px-3 py-1">{t('currentPlan')}</Badge>
              </div>
            )}
            
            <CardHeader className="text-center pb-4">
              <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <plan.icon className="w-8 h-8 text-gray-600" />
              </div>
              <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
              <CardDescription className="text-gray-600">{plan.description}</CardDescription>
              <div className="mt-4">
                <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                <span className="text-gray-600 ml-2">/{plan.period}</span>
              </div>
              <p className="text-sm text-gray-500 mt-2">{plan.tokens} tokens included</p>
            </CardHeader>

            <CardContent className="space-y-6">
              <div className="space-y-3">
                {plan.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>

              {plan.limitations.length > 0 && (
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-800">Limitations:</p>
                  {plan.limitations.map((limitation, limitIndex) => (
                    <p key={limitIndex} className="text-sm text-gray-600">• {limitation}</p>
                  ))}
                </div>
              )}

              <Button 
                className={`w-full ${plan.buttonColor} font-semibold py-3`}
                disabled={plan.current}
              >
                {plan.current ? t('currentPlan') : plan.buttonText}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* FAQ Section */}
      <div className="bg-gray-50 rounded-2xl p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
          {faq.frequentlyAskedQuestions}
        </h2>
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">{faq.whatAreTokens}</h3>
            <p className="text-gray-600 text-sm">
              {faq.tokensExplanation}
            </p>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">{faq.canIChangePlans}</h3>
            <p className="text-gray-600 text-sm">
              {faq.changePlansAnswer}
            </p>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">{faq.whatIfExceedLimit}</h3>
            <p className="text-gray-600 text-sm">
              {faq.exceedLimitAnswer}
            </p>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">{faq.isThereSetupFee}</h3>
            <p className="text-gray-600 text-sm">
              {faq.setupFeeAnswer}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Pricing;
