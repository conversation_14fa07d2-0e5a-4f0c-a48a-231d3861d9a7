import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useBots } from '@/hooks/useBots';
import { useLanguage } from '@/contexts/LanguageContext';
import { 
  MessageSquare, 
  Zap, 
  Settings, 
  Upload,
  Bot,
  ArrowRight,
  CheckCircle
} from 'lucide-react';

const CreateBot = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { createBot } = useBots();
  const { t, isRTL, direction } = useLanguage();
  
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [botData, setBotData] = useState({
    name: '',
    description: '',
    platform: '',
    businessType: '',
    tone: 'friendly',
    language: 'en'
  });

  const platforms = [
    { id: 'whatsapp', name: 'WhatsApp', icon: MessageSquare, color: 'bg-green-500' },
    { id: 'facebook', name: 'Facebook Messenger', icon: MessageSquare, color: 'bg-blue-500' },
    { id: 'sms', name: 'SMS', icon: MessageSquare, color: 'bg-purple-500' }
  ];

  const businessTypes = [
    'Restaurant', 'E-commerce', 'Healthcare', 'Education', 'Real Estate', 
    'Finance', 'Travel', 'Technology', 'Consulting', 'Other'
  ];

  const tones = [
    { id: 'friendly', name: 'Friendly & Casual', desc: 'Warm and approachable' },
    { id: 'professional', name: 'Professional', desc: 'Formal and business-like' },
    { id: 'helpful', name: 'Helpful & Supportive', desc: 'Focused on assistance' },
    { id: 'enthusiastic', name: 'Enthusiastic', desc: 'Energetic and positive' }
  ];

  const handleInputChange = (field: string, value: string) => {
    setBotData(prev => ({ ...prev, [field]: value }));
  };

  const handleNext = () => {
    if (step < 3) {
      setStep(step + 1);
    }
  };

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const handleCreateBot = async () => {
    if (!botData.name || !botData.platform) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    try {
      const bot = await createBot(botData.name, botData.platform as any);
      if (bot) {
        toast({
          title: "Bot Created Successfully!",
          description: `${botData.name} has been created and is ready to configure.`,
        });
        navigate(`/bot/${bot.id}/configure`);
      }
    } catch (error) {
      console.error('Error creating bot:', error);
      toast({
        title: "Creation Failed",
        description: "Failed to create bot. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const isStepValid = () => {
    switch (step) {
      case 1:
        return botData.name && botData.description;
      case 2:
        return botData.platform && botData.businessType;
      case 3:
        return true;
      default:
        return false;
    }
  };

  return (
    <div className="space-y-6" dir={direction}>
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">{t('createChatbot')}</h1>
        <p className="text-gray-600">Set up your intelligent chatbot in just a few steps</p>
      </div>

      {/* Progress Steps */}
      <div className="flex justify-center mb-8">
        <div className={`flex items-center space-x-4 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
          {[1, 2, 3].map((stepNum) => (
            <div key={stepNum} className={`flex items-center ${stepNum < 3 ? 'space-x-4' : ''} ${isRTL && stepNum < 3 ? 'space-x-reverse' : ''}`}>
              <div className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold ${
                step >= stepNum 
                  ? 'bg-teal-500 text-white' 
                  : 'bg-gray-200 text-gray-600'
              }`}>
                {step > stepNum ? <CheckCircle className="w-5 h-5" /> : stepNum}
              </div>
              {stepNum < 3 && (
                <div className={`w-16 h-1 ${step > stepNum ? 'bg-teal-500' : 'bg-gray-200'}`} />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {step === 1 && <Bot className="w-5 h-5" />}
            {step === 2 && <Settings className="w-5 h-5" />}
            {step === 3 && <Zap className="w-5 h-5" />}
            {step === 1 && "Basic Information"}
            {step === 2 && "Platform & Business"}
            {step === 3 && "Personality & Style"}
          </CardTitle>
          <CardDescription>
            {step === 1 && "Tell us about your chatbot"}
            {step === 2 && "Choose your platform and business type"}
            {step === 3 && "Customize your bot's personality"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Step 1: Basic Information */}
          {step === 1 && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Bot Name *</Label>
                <Input
                  id="name"
                  placeholder="e.g., Restaurant Assistant"
                  value={botData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  placeholder="Describe what your bot will help with..."
                  value={botData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={3}
                />
              </div>
            </div>
          )}

          {/* Step 2: Platform & Business */}
          {step === 2 && (
            <div className="space-y-6">
              <div>
                <Label>Choose Platform *</Label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2">
                  {platforms.map((platform) => (
                    <div
                      key={platform.id}
                      className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                        botData.platform === platform.id
                          ? 'border-teal-500 bg-teal-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleInputChange('platform', platform.id)}
                    >
                      <div className="flex items-center space-x-3">
                        <div className={`w-10 h-10 ${platform.color} rounded-lg flex items-center justify-center`}>
                          <platform.icon className="w-5 h-5 text-white" />
                        </div>
                        <span className="font-medium">{platform.name}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <Label htmlFor="businessType">Business Type *</Label>
                <Select value={botData.businessType} onValueChange={(value) => handleInputChange('businessType', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select your business type" />
                  </SelectTrigger>
                  <SelectContent>
                    {businessTypes.map((type) => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          {/* Step 3: Personality */}
          {step === 3 && (
            <div className="space-y-6">
              <div>
                <Label>Bot Personality</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  {tones.map((tone) => (
                    <div
                      key={tone.id}
                      className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                        botData.tone === tone.id
                          ? 'border-teal-500 bg-teal-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleInputChange('tone', tone.id)}
                    >
                      <h3 className="font-medium">{tone.name}</h3>
                      <p className="text-sm text-gray-600 mt-1">{tone.desc}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className={`flex justify-between pt-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Button
              variant="outline"
              onClick={handleBack}
              disabled={step === 1}
            >
              Back
            </Button>
            
            {step < 3 ? (
              <Button
                onClick={handleNext}
                disabled={!isStepValid()}
                className="bg-teal-500 hover:bg-teal-600"
              >
                Next <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            ) : (
              <Button
                onClick={handleCreateBot}
                disabled={loading}
                className="bg-teal-500 hover:bg-teal-600"
              >
                {loading ? 'Creating...' : 'Create Bot'}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CreateBot;
