
import { useState } from "react";
import { Send, Smartphone, MessageCircle, MessageSquare } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const BotTesting = () => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Hello! I'm your Restaurant Assistant. How can I help you today?",
      sender: "bot",
      timestamp: new Date(Date.now() - 60000)
    }
  ]);
  const [inputMessage, setInputMessage] = useState("");
  const [selectedChannel, setSelectedChannel] = useState("whatsapp");

  const channels = [
    { id: "whatsapp", name: "WhatsApp", icon: MessageCircle, color: "bg-green-500" },
    { id: "facebook", name: "Facebook", icon: MessageSquare, color: "bg-blue-500" },
    { id: "sms", name: "SMS", icon: Smartphone, color: "bg-purple-500" }
  ];

  const sendMessage = () => {
    if (!inputMessage.trim()) return;

    const userMessage = {
      id: messages.length + 1,
      text: inputMessage,
      sender: "user" as const,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage("");

    // Simulate bot response
    setTimeout(() => {
      const botResponse = {
        id: messages.length + 2,
        text: getBotResponse(inputMessage),
        sender: "bot" as const,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botResponse]);
    }, 1000);
  };

  const getBotResponse = (userMessage: string) => {
    const message = userMessage.toLowerCase();
    if (message.includes("menu") || message.includes("food")) {
      return "Here's our menu! We offer delicious pasta, pizza, salads, and daily specials. Would you like to see our full menu or hear about today's specials?";
    } else if (message.includes("hours") || message.includes("open")) {
      return "We're open Monday-Sunday from 11:30 AM to 10:00 PM. Kitchen closes at 9:30 PM. Would you like to make a reservation?";
    } else if (message.includes("reservation") || message.includes("book")) {
      return "I'd be happy to help you make a reservation! What date and time would you prefer, and how many people will be joining you?";
    } else {
      return "Thank you for your message! I can help you with our menu, hours, reservations, and location. What would you like to know?";
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Test Your Bot</h1>
        <p className="text-gray-600 mt-1">Try out your chatbot and see how it responds</p>
      </div>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* Channel Selection */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Simulate Channel</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {channels.map((channel) => (
                <button
                  key={channel.id}
                  onClick={() => setSelectedChannel(channel.id)}
                  className={`w-full flex items-center gap-3 p-3 rounded-lg border-2 transition-all ${
                    selectedChannel === channel.id
                      ? "border-teal-500 bg-teal-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                >
                  <div className={`w-10 h-10 ${channel.color} rounded-lg flex items-center justify-center`}>
                    <channel.icon className="w-5 h-5 text-white" />
                  </div>
                  <div className="text-left">
                    <p className="font-medium text-gray-900">{channel.name}</p>
                    <p className="text-sm text-gray-500">Test on {channel.name}</p>
                  </div>
                </button>
              ))}
            </CardContent>
          </Card>

          {/* Bot Info */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="text-lg">Current Bot</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <MessageCircle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Restaurant Assistant</p>
                  <Badge className="bg-green-100 text-green-800">Active</Badge>
                </div>
              </div>
              <div className="space-y-2 text-sm text-gray-600">
                <p><span className="font-medium">Files:</span> menu.pdf, hours.txt</p>
                <p><span className="font-medium">Last Updated:</span> 2 hours ago</p>
                <p><span className="font-medium">Confidence:</span> 95%</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Chat Interface */}
        <div className="lg:col-span-2">
          <Card className="h-[600px] flex flex-col">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <MessageCircle className="w-5 h-5" />
                  Chat Test - {channels.find(c => c.id === selectedChannel)?.name}
                </CardTitle>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setMessages([{
                    id: 1,
                    text: "Hello! I'm your Restaurant Assistant. How can I help you today?",
                    sender: "bot",
                    timestamp: new Date()
                  }])}
                >
                  Clear Chat
                </Button>
              </div>
            </CardHeader>

            <CardContent className="flex-1 flex flex-col">
              {/* Messages */}
              <div className="flex-1 overflow-y-auto space-y-4 mb-4 pr-2">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-2xl ${
                        message.sender === 'user'
                          ? 'bg-teal-500 text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      <p className="text-sm">{message.text}</p>
                      <p className={`text-xs mt-1 ${
                        message.sender === 'user' ? 'text-teal-100' : 'text-gray-500'
                      }`}>
                        {formatTime(message.timestamp)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Input */}
              <div className="flex gap-2">
                <Input
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  placeholder="Type your message..."
                  onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                  className="flex-1"
                />
                <Button 
                  onClick={sendMessage}
                  disabled={!inputMessage.trim()}
                  className="bg-teal-500 hover:bg-teal-600"
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default BotTesting;
