
import { ChatInterface } from "@/components/ChatInterface";
import { Button } from "@/components/ui/button";
import { MessageSquare, Zap, Shield } from "lucide-react";
import { Link } from "react-router-dom";
import { useLanguage } from "@/contexts/LanguageContext";
import { LanguageSelector } from "@/components/LanguageSelector";

const Landing = () => {
  const { t, isRTL, direction } = useLanguage();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 relative overflow-hidden" dir={direction}>

      {/* Header */}
      <header className="px-6 py-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-gradient-to-br from-teal-500 via-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
              <MessageSquare className="w-7 h-7 text-white" />
            </div>
            <span className="font-bold text-3xl bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">Chatlink</span>
          </div>
          <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <LanguageSelector variant="compact" />
            <Button
              variant="outline"
              className="border-2 border-teal-500 text-teal-600 hover:bg-teal-500 hover:text-white transition-all duration-300 rounded-xl px-6 py-2 font-semibold shadow-lg hover:shadow-xl hover:scale-105"
              asChild
            >
              <Link to="/signin">{t('signIn')}</Link>
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-20">
        <div className="grid lg:grid-cols-2 gap-20 items-center">
          {/* Left Side - Content */}
          <div className="space-y-10">
            <div className={`space-y-8 ${isRTL ? 'hero-content-rtl' : ''}`}>
              <h1 className="text-6xl lg:text-7xl font-bold leading-tight">
                <span className="text-slate-800">{t('heroTitle1')}</span>
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-teal-500 via-blue-500 to-purple-600">
                  {t('heroTitle2')}
                </span>
                <br />
                <span className="text-slate-800">{t('heroTitle3')}</span>
              </h1>

              <p className="text-xl text-slate-600 leading-relaxed max-w-lg">
                {t('heroSubtitle')}
              </p>

              <div className={`flex gap-4 ${isRTL ? 'hero-buttons-rtl' : ''}`}>
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white px-8 py-4 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                  asChild
                >
                  <Link to="/signup">{t('getStarted')}</Link>
                </Button>
              </div>
            </div>

            {/* Features */}
            <div className="space-y-6">
              <div className="flex items-center gap-5 p-4 rounded-2xl bg-white/80 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                <div className="w-14 h-14 bg-gradient-to-br from-teal-500 to-teal-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <Zap className="w-7 h-7 text-white" />
                </div>
                <div>
                  <h3 className="font-bold text-slate-800 text-lg">Instant Setup</h3>
                  <p className="text-slate-600">Get your chatbot ready in minutes, not hours</p>
                </div>
              </div>

              <div className="flex items-center gap-5 p-4 rounded-2xl bg-white/80 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <MessageSquare className="w-7 h-7 text-white" />
                </div>
                <div>
                  <h3 className="font-bold text-slate-800 text-lg">Multi-Platform</h3>
                  <p className="text-slate-600">Works on WhatsApp, Facebook, and SMS</p>
                </div>
              </div>

              <div className="flex items-center gap-5 p-4 rounded-2xl bg-white/80 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                <div className="w-14 h-14 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <Shield className="w-7 h-7 text-white" />
                </div>
                <div>
                  <h3 className="font-bold text-slate-800 text-lg">Secure & Private</h3>
                  <p className="text-slate-600">Your data stays protected and confidential</p>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Chat Interface */}
          <ChatInterface />
        </div>
      </div>

      {/* Trusted By Section */}
      <div className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-6 text-center">
          <p className="text-gray-500 mb-8">Trusted by 500+ small businesses worldwide</p>
          <div className="flex justify-center items-center gap-8 opacity-60">
            <div className="w-24 h-12 bg-gray-200 rounded flex items-center justify-center">Logo</div>
            <div className="w-24 h-12 bg-gray-200 rounded flex items-center justify-center">Logo</div>
            <div className="w-24 h-12 bg-gray-200 rounded flex items-center justify-center">Logo</div>
            <div className="w-24 h-12 bg-gray-200 rounded flex items-center justify-center">Logo</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Landing;
