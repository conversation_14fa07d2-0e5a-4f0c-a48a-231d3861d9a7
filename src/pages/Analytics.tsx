
import { Bar<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>U<PERSON>, <PERSON>, MessageSquare, Clock, Target } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useLanguage } from "@/contexts/LanguageContext";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts';

const Analytics = () => {
  const { t, isRTL, direction } = useLanguage();

  const exportReport = () => {
    // Create HTML content for the report
    const reportHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Chatlink Analytics Report</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; }
          .metric { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
          .metric h3 { margin: 0 0 10px 0; color: #333; }
          .metric .value { font-size: 24px; font-weight: bold; color: #14B8A6; }
          .metric .change { color: #059669; margin-top: 5px; }
          .chart-section { margin: 30px 0; }
          .bot-performance { margin: 10px 0; padding: 10px; background: #f9f9f9; border-radius: 6px; }
        </style>
      </head>
      <body>
        <h1>Chatlink Analytics Report</h1>
        <p>Generated on ${new Date().toLocaleString()}</p>
        
        <h2>Key Metrics</h2>
        <div class="metric">
          <h3>Total Conversations</h3>
          <div class="value">3,847</div>
          <div class="change">+23% vs last period</div>
        </div>
        <div class="metric">
          <h3>Active Users</h3>
          <div class="value">1,284</div>
          <div class="change">+18% vs last period</div>
        </div>
        <div class="metric">
          <h3>Average Response Time</h3>
          <div class="value">2.1s</div>
          <div class="change">-12% vs last period</div>
        </div>
        <div class="metric">
          <h3>Resolution Rate</h3>
          <div class="value">89%</div>
          <div class="change">+5% vs last period</div>
        </div>
        
        <h2>Bot Performance</h2>
        <div class="bot-performance">
          <h3>Restaurant Assistant</h3>
          <p>Messages: 1,850 | Satisfaction: 4.8/5.0 | Response Time: 2.3s</p>
        </div>
        <div class="bot-performance">
          <h3>Plumbing Services</h3>
          <p>Messages: 1,200 | Satisfaction: 4.6/5.0 | Response Time: 1.8s</p>
        </div>
        <div class="bot-performance">
          <h3>Hair Salon Bot</h3>
          <p>Messages: 650 | Satisfaction: 4.4/5.0 | Response Time: 3.1s</p>
        </div>
        
        <h2>Channel Distribution</h2>
        <p>WhatsApp: 45% | Facebook: 35% | SMS: 20%</p>
      </body>
      </html>
    `;
    
    // Create and download the file
    const blob = new Blob([reportHTML], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chatlink-analytics-${new Date().toISOString().split('T')[0]}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };
  const monthlyData = [
    { month: 'Jan', messages: 1200, users: 89 },
    { month: 'Feb', messages: 1800, users: 124 },
    { month: 'Mar', messages: 2400, users: 157 },
    { month: 'Apr', messages: 2100, users: 143 },
    { month: 'May', messages: 2800, users: 189 },
    { month: 'Jun', messages: 3200, users: 221 }
  ];

  const channelData = [
    { name: 'WhatsApp', value: 45, color: '#25D366' },
    { name: 'Facebook', value: 35, color: '#1877F2' },
    { name: 'SMS', value: 20, color: '#FF6B6B' }
  ];

  const botPerformance = [
    { name: 'Restaurant Assistant', messages: 1850, satisfaction: 4.8, responseTime: '2.3s' },
    { name: 'Plumbing Services', messages: 1200, satisfaction: 4.6, responseTime: '1.8s' },
    { name: 'Hair Salon Bot', messages: 650, satisfaction: 4.4, responseTime: '3.1s' }
  ];

  const metrics = [
    {
      title: t('totalConversations'),
      value: "3,847",
      change: "+23%",
      icon: MessageSquare,
      color: "text-blue-600"
    },
    {
      title: t('activeUsers'),
      value: "1,284",
      change: "+18%",
      icon: Users,
      color: "text-green-600"
    },
    {
      title: t('avgResponseTime'),
      value: "2.1s",
      change: "-12%",
      icon: Clock,
      color: "text-purple-600"
    },
    {
      title: t('resolutionRate'),
      value: "89%",
      change: "+5%",
      icon: Target,
      color: "text-orange-600"
    }
  ];

  return (
    <div className="space-y-6" dir={direction}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{t('analytics')}</h1>
          <p className="text-gray-600 mt-1">{t('trackPerformance')}</p>
        </div>
        <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <Select>
            <SelectTrigger className="w-40">
              <SelectValue placeholder={t('last30Days')} />
            </SelectTrigger>
            <SelectContent className="bg-white">
              <SelectItem value="7d">{t('last7Days')}</SelectItem>
              <SelectItem value="30d">{t('last30Days')}</SelectItem>
              <SelectItem value="90d">{t('last90Days')}</SelectItem>
              <SelectItem value="1y">{t('lastYear')}</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={exportReport}>{t('exportReport')}</Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                  <p className="text-3xl font-bold text-gray-900 mt-1">{metric.value}</p>
                  <p className={`text-sm mt-1 ${metric.change.startsWith('+') ? 'text-green-600' : metric.change.startsWith('-') ? 'text-red-600' : 'text-gray-600'}`}>
                    {metric.change} {t('vsLastPeriod')}
                  </p>
                </div>
                <div className="p-3 rounded-lg bg-gray-50">
                  <metric.icon className={`w-6 h-6 ${metric.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        {/* Messages Over Time */}
        <Card>
          <CardHeader>
            <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <TrendingUp className="w-5 h-5" />
              {t('messagesUsersOverTime')}
            </CardTitle>
            <CardDescription>{t('monthlyTrends')}</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={monthlyData} margin={{ top: 20, right: isRTL ? 10 : 30, left: isRTL ? 30 : 10, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis orientation={isRTL ? 'right' : 'left'} />
                <Tooltip />
                <Bar dataKey="messages" fill="#14B8A6" name={t('messages')} />
                <Bar dataKey="users" fill="#3B82F6" name={t('activeUsers')} />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Channel Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>{t('channelDistribution')}</CardTitle>
            <CardDescription>{t('messagesByChannel')}</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={channelData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}%`}
                  labelLine={false}
                  style={{ fontSize: '12px' }}
                >
                  {channelData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Bot Performance Table */}
      <Card>
        <CardHeader>
          <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <BarChart3 className="w-5 h-5" />
            {t('botPerformance')}
          </CardTitle>
          <CardDescription>{t('individualBotStats')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {botPerformance.map((bot, index) => (
              <div key={index} className={`flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <MessageSquare className="w-6 h-6 text-white" />
                  </div>
                  <div className={isRTL ? 'text-right' : 'text-left'}>
                    <h3 className="font-semibold text-gray-900">{bot.name}</h3>
                    <p className="text-sm text-gray-600">{bot.messages} {t('totalMessages')}</p>
                  </div>
                </div>

                <div className={`flex items-center gap-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className="text-center">
                    <p className="text-sm font-medium text-gray-900">{bot.satisfaction}/5.0</p>
                    <p className="text-xs text-gray-500">{t('satisfaction')}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm font-medium text-gray-900">{bot.responseTime}</p>
                    <p className="text-xs text-gray-500">{t('avgResponse')}</p>
                  </div>
                  <Badge
                    variant="secondary"
                    className="bg-green-100 text-green-800"
                  >
                    {t('active')}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Analytics;
