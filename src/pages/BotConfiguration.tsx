import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useBots } from '@/hooks/useBots';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/contexts/AuthContext';
import { 
  Bot, 
  Upload, 
  MessageSquare, 
  FileText, 
  Send, 
  TestTube,
  ArrowLeft,
  Trash2,
  Edit3,
  Save,
  X
} from 'lucide-react';

interface ChatMessage {
  id: string;
  content: string;
  timestamp: Date;
  type: 'user' | 'system';
}

interface Document {
  id: string;
  bot_id: string;
  user_id: string;
  type: 'chat' | 'file';
  file_name?: string;
  file_url?: string;
  content?: string;
  embedding_status: 'pending' | 'processing' | 'completed' | 'failed';
  created_at: string;
}

const BotConfiguration = () => {
  const { botId } = useParams<{ botId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();
  const { bots, getBotById, updateBot, retrainBot } = useBots();
  const { t, isRTL, direction } = useLanguage();
  
  const [bot, setBot] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [retraining, setRetraining] = useState(false);
  
  // Chat state
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [currentMessage, setCurrentMessage] = useState('');
  const chatEndRef = useRef<HTMLDivElement>(null);
  
  // File upload state
  const [uploadedFiles, setUploadedFiles] = useState<Document[]>([]);
  const [dragActive, setDragActive] = useState(false);
  
  // Edit mode state
  const [editMode, setEditMode] = useState(false);
  const [editedBot, setEditedBot] = useState({
    name: '',
    description: '',
    tone: '',
    language: ''
  });

  // Documents state
  const [documents, setDocuments] = useState<Document[]>([]);

  useEffect(() => {
    loadBotData();
  }, [botId]);

  useEffect(() => {
    scrollToBottom();
  }, [chatMessages]);

  const scrollToBottom = () => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadBotData = async () => {
    if (!botId) return;
    
    try {
      setLoading(true);
      const botData = await getBotById(botId);
      if (botData) {
        setBot(botData);
        setEditedBot({
          name: botData.name || '',
          description: botData.description || '',
          tone: botData.tone || 'friendly',
          language: botData.language || 'en'
        });
        
        // Load existing documents and chat history
        await loadDocuments();
      }
    } catch (error) {
      console.error('Error loading bot:', error);
      toast({
        title: t('error'),
        description: 'Failed to load bot configuration',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const loadDocuments = async () => {
    // TODO: Implement loading documents from Supabase
    // This would fetch from the documents table where bot_id = botId
    try {
      // Placeholder for now - in real implementation, this would be:
      // const { data } = await supabase
      //   .from('documents')
      //   .select('*')
      //   .eq('bot_id', botId)
      //   .order('created_at', { ascending: true });
      
      // For now, we'll use mock data
      const mockDocuments: Document[] = [];
      setDocuments(mockDocuments);
      
      // Separate chat messages and files
      const chatDocs = mockDocuments.filter(doc => doc.type === 'chat');
      const fileDocs = mockDocuments.filter(doc => doc.type === 'file');
      
      setUploadedFiles(fileDocs);
      
      // Convert chat documents back to messages
      const messages: ChatMessage[] = chatDocs.map(doc => ({
        id: doc.id,
        content: doc.content || '',
        timestamp: new Date(doc.created_at),
        type: 'user'
      }));
      setChatMessages(messages);
      
    } catch (error) {
      console.error('Error loading documents:', error);
    }
  };

  const handleSendMessage = () => {
    if (!currentMessage.trim()) return;

    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      content: currentMessage,
      timestamp: new Date(),
      type: 'user'
    };

    setChatMessages(prev => [...prev, newMessage]);
    setCurrentMessage('');

    // Add system response
    setTimeout(() => {
      const systemMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: t('chatbotGreeting'),
        timestamp: new Date(),
        type: 'system'
      };
      setChatMessages(prev => [...prev, systemMessage]);
    }, 1000);
  };

  const handleSaveChat = async () => {
    if (chatMessages.length === 0) {
      toast({
        title: t('error'),
        description: 'No chat messages to save',
        variant: 'destructive'
      });
      return;
    }

    setSaving(true);
    try {
      // Concatenate all chat messages
      const chatContent = chatMessages
        .filter(msg => msg.type === 'user')
        .map(msg => msg.content)
        .join('\n\n');

      // TODO: Save to Supabase documents table
      // const { data, error } = await supabase
      //   .from('documents')
      //   .insert({
      //     bot_id: botId,
      //     user_id: user?.id,
      //     type: 'chat',
      //     content: chatContent,
      //     embedding_status: 'pending'
      //   });

      toast({
        title: t('success'),
        description: t('chatSaved'),
      });

      // Clear chat after saving
      setChatMessages([]);
      
    } catch (error) {
      console.error('Error saving chat:', error);
      toast({
        title: t('error'),
        description: 'Failed to save chat',
        variant: 'destructive'
      });
    } finally {
      setSaving(false);
    }
  };

  const handleFileUpload = async (files: FileList) => {
    if (!files.length) return;

    setUploading(true);
    try {
      for (const file of Array.from(files)) {
        // Validate file type
        const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'];
        if (!allowedTypes.includes(file.type)) {
          toast({
            title: t('error'),
            description: t('unsupportedFileType'),
            variant: 'destructive'
          });
          continue;
        }

        // Validate file size (10MB)
        if (file.size > 10 * 1024 * 1024) {
          toast({
            title: t('error'),
            description: t('fileTooLarge'),
            variant: 'destructive'
          });
          continue;
        }

        // TODO: Upload to Supabase Storage and save to documents table
        // const { data: uploadData, error: uploadError } = await supabase.storage
        //   .from('documents')
        //   .upload(`${botId}/${file.name}`, file);

        // const { data: docData, error: docError } = await supabase
        //   .from('documents')
        //   .insert({
        //     bot_id: botId,
        //     user_id: user?.id,
        //     type: 'file',
        //     file_name: file.name,
        //     file_url: uploadData?.path,
        //     embedding_status: 'pending'
        //   });

        // For now, add to local state
        const mockDoc: Document = {
          id: Date.now().toString(),
          bot_id: botId!,
          user_id: user?.id || '',
          type: 'file',
          file_name: file.name,
          file_url: `mock-url/${file.name}`,
          embedding_status: 'completed',
          created_at: new Date().toISOString()
        };

        setUploadedFiles(prev => [...prev, mockDoc]);
      }

      toast({
        title: t('success'),
        description: t('filesUploaded'),
      });

    } catch (error) {
      console.error('Error uploading files:', error);
      toast({
        title: t('error'),
        description: t('uploadError'),
        variant: 'destructive'
      });
    } finally {
      setUploading(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
    handleFileUpload(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  };

  const handleDeleteFile = async (fileId: string) => {
    try {
      // TODO: Delete from Supabase
      setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
      toast({
        title: t('success'),
        description: 'File deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting file:', error);
    }
  };

  const handleSaveBotDetails = async () => {
    setSaving(true);
    try {
      await updateBot(botId!, editedBot);
      setBot({ ...bot, ...editedBot });
      setEditMode(false);
      toast({
        title: t('success'),
        description: t('botUpdated'),
      });
    } catch (error) {
      console.error('Error updating bot:', error);
      toast({
        title: t('error'),
        description: 'Failed to update bot',
        variant: 'destructive'
      });
    } finally {
      setSaving(false);
    }
  };

  const handleRetrainBot = async () => {
    if (!botId) return;

    setRetraining(true);
    try {
      const success = await retrainBot(botId);
      if (success) {
        toast({
          title: t('success'),
          description: t('trainingStarted'),
        });
        // Reload bot data to get updated training status
        await loadBotData();
      }
    } catch (error) {
      console.error('Error retraining bot:', error);
    } finally {
      setRetraining(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Bot className="w-8 h-8 animate-spin mx-auto mb-2" />
          <p>{t('loading')}</p>
        </div>
      </div>
    );
  }

  if (!bot) {
    return (
      <div className="text-center py-12">
        <Bot className="w-12 h-12 mx-auto mb-4 text-gray-400" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Bot not found</h2>
        <p className="text-gray-600 mb-4">The bot you're looking for doesn't exist.</p>
        <Button onClick={() => navigate('/dashboard')}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Dashboard
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6" dir={direction}>
      {/* Header */}
      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <Button
            variant="outline"
            onClick={() => navigate('/dashboard')}
            className={`${isRTL ? 'flex-row-reverse' : ''}`}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            {t('backToBotList')}
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{t('configureYourBot')}</h1>
            <p className="text-gray-600">{t('setupBotKnowledge')}</p>
          </div>
        </div>
        <Button
          onClick={() => navigate(`/bot/${botId}/test`)}
          className="bg-teal-500 hover:bg-teal-600"
        >
          <TestTube className="w-4 h-4 mr-2" />
          {t('testYourBot')}
        </Button>
      </div>

      {/* Bot Details Card */}
      <Card>
        <CardHeader>
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-blue-600 rounded-lg flex items-center justify-center">
                <Bot className="w-6 h-6 text-white" />
              </div>
              <div className={isRTL ? 'text-right' : 'text-left'}>
                {editMode ? (
                  <Input
                    value={editedBot.name}
                    onChange={(e) => setEditedBot(prev => ({ ...prev, name: e.target.value }))}
                    className="text-xl font-bold"
                  />
                ) : (
                  <CardTitle className="text-xl">{bot.name}</CardTitle>
                )}
                {editMode ? (
                  <Textarea
                    value={editedBot.description}
                    onChange={(e) => setEditedBot(prev => ({ ...prev, description: e.target.value }))}
                    className="mt-1"
                    rows={2}
                  />
                ) : (
                  <CardDescription>{bot.description}</CardDescription>
                )}
              </div>
            </div>
            <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              {editMode ? (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setEditMode(false)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleSaveBotDetails}
                    disabled={saving}
                  >
                    <Save className="w-4 h-4 mr-2" />
                    {saving ? t('saving') : t('save')}
                  </Button>
                </>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setEditMode(true)}
                >
                  <Edit3 className="w-4 h-4 mr-2" />
                  {t('edit')}
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Training Status Card */}
      <Card>
        <CardHeader>
          <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Bot className="w-5 h-5" />
            {t('trainingStatus')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
              {bot?.training_status === 'processing' && (
                <>
                  <div className="w-6 h-6 border-2 border-yellow-500 border-t-transparent rounded-full animate-spin"></div>
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-200">
                    {t('trainingInProgress')}
                  </Badge>
                </>
              )}
              {bot?.training_status === 'complete' && (
                <>
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200">
                    {t('trainingComplete')}
                  </Badge>
                </>
              )}
              {bot?.training_status === 'failed' && (
                <>
                  <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </div>
                  <Badge variant="secondary" className="bg-red-100 text-red-800 border-red-200">
                    {t('trainingFailed')}
                  </Badge>
                </>
              )}
              {(!bot?.training_status || bot?.training_status === 'pending') && (
                <>
                  <div className="w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center">
                    <Bot className="w-4 h-4 text-white" />
                  </div>
                  <Badge variant="secondary" className="bg-gray-100 text-gray-800 border-gray-200">
                    Ready to train
                  </Badge>
                </>
              )}
              {bot?.training_updated_at && (
                <span className="text-sm text-gray-500">
                  Last updated: {new Date(bot.training_updated_at).toLocaleString()}
                </span>
              )}
            </div>

            {bot?.training_status === 'failed' && (
              <Button
                onClick={handleRetrainBot}
                disabled={retraining}
                variant="outline"
                size="sm"
                className="text-red-600 border-red-200 hover:bg-red-50"
              >
                {retraining ? t('retrainingBot') : t('retryTraining')}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Chat Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <MessageSquare className="w-5 h-5" />
              {t('businessInformation')}
            </CardTitle>
            <CardDescription>
              {t('tellBotAboutBusiness')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Chat Messages */}
            <div className="h-64 border rounded-lg p-4 overflow-y-auto bg-gray-50">
              {chatMessages.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>{t('startConversationAboutBusiness')}</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {chatMessages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.type === 'user' 
                        ? (isRTL ? 'justify-start' : 'justify-end') 
                        : (isRTL ? 'justify-end' : 'justify-start')
                      }`}
                    >
                      <div
                        className={`max-w-xs px-3 py-2 rounded-lg ${
                          message.type === 'user'
                            ? 'bg-teal-500 text-white'
                            : 'bg-white border'
                        }`}
                      >
                        <p className="text-sm">{message.content}</p>
                        <p className="text-xs opacity-70 mt-1">
                          {message.timestamp.toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  ))}
                  <div ref={chatEndRef} />
                </div>
              )}
            </div>

            {/* Chat Input */}
            <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Input
                value={currentMessage}
                onChange={(e) => setCurrentMessage(e.target.value)}
                placeholder={t('typeAboutYourBusiness')}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                className="flex-1"
              />
              <Button
                onClick={handleSendMessage}
                disabled={!currentMessage.trim()}
                size="sm"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>

            {/* Save Chat Button */}
            {chatMessages.length > 0 && (
              <Button
                onClick={handleSaveChat}
                disabled={saving}
                className="w-full"
                variant="outline"
              >
                {saving ? t('saving') : t('saveChatHistory')}
              </Button>
            )}
          </CardContent>
        </Card>

        {/* File Upload */}
        <Card>
          <CardHeader>
            <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <FileText className="w-5 h-5" />
              {t('uploadDocuments')}
            </CardTitle>
            <CardDescription>
              {t('uploadBusinessDocuments')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* File Upload Area */}
            <div
              className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                dragActive ? 'border-teal-500 bg-teal-50' : 'border-gray-300'
              }`}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
            >
              <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm text-gray-600 mb-2">
                {t('dragDropHere')} <span className="text-teal-500 cursor-pointer">{t('clickToSelect')}</span>
              </p>
              <p className="text-xs text-gray-500">{t('supportedFileTypes')}</p>
              <input
                type="file"
                multiple
                accept=".pdf,.docx,.txt"
                onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
                className="hidden"
                id="file-upload"
              />
              <label htmlFor="file-upload">
                <Button variant="outline" size="sm" className="mt-2" asChild>
                  <span>{t('selectFiles')}</span>
                </Button>
              </label>
            </div>

            {/* Uploaded Files */}
            {uploadedFiles.length > 0 && (
              <div className="space-y-2">
                <Label>{t('uploadedFiles')}</Label>
                {uploadedFiles.map((file) => (
                  <div
                    key={file.id}
                    className={`flex items-center justify-between p-3 border rounded-lg ${isRTL ? 'flex-row-reverse' : ''}`}
                  >
                    <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <FileText className="w-4 h-4 text-gray-500" />
                      <span className="text-sm">{file.file_name}</span>
                      <Badge variant="secondary" className="text-xs">
                        {file.embedding_status}
                      </Badge>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteFile(file.id)}
                    >
                      <Trash2 className="w-4 h-4 text-red-500" />
                    </Button>
                  </div>
                ))}
              </div>
            )}

            {uploading && (
              <div className="text-center py-4">
                <Upload className="w-6 h-6 animate-pulse mx-auto mb-2" />
                <p className="text-sm text-gray-600">{t('uploading')}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Retrain Bot Button */}
      <div className="flex justify-center pt-6">
        <Button
          onClick={handleRetrainBot}
          disabled={retraining || bot?.training_status === 'processing'}
          className="bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white px-8 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
          size="lg"
        >
          {retraining || bot?.training_status === 'processing' ? (
            <>
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              {t('retrainingBot')}
            </>
          ) : (
            <>
              <Bot className="w-5 h-5 mr-2" />
              {t('retrainBot')}
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default BotConfiguration;
