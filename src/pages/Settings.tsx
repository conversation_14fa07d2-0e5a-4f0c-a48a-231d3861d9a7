
import { useState, useEffect } from "react";
import { Settings as Setting<PERSON><PERSON><PERSON>, User, Bell, CreditCard, Shield, Key, Eye, EyeOff } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useNavigate } from "react-router-dom";

const Settings = () => {
  const { user, signOut } = useAuth();
  const { t, isRTL, direction } = useLanguage();
  const { toast } = useToast();
  const navigate = useNavigate();

  // Form state
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    businessName: "",
    timezone: "est"
  });

  // Notification preferences state
  const [notifications, setNotifications] = useState({
    newMessages: true,
    botPerformance: true,
    usageWarnings: true,
    weeklyReports: false
  });

  // Password change state
  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: ""
  });
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });

  const [loading, setLoading] = useState(false);
  const [passwordLoading, setPasswordLoading] = useState(false);
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [activeTab, setActiveTab] = useState("profile");

  // Load user data when component mounts or user changes
  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.user_metadata?.first_name || "",
        lastName: user.user_metadata?.last_name || "",
        email: user.email || "",
        businessName: user.user_metadata?.business_name || "",
        timezone: user.user_metadata?.timezone || "est"
      });
    }
  }, [user]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleNotificationChange = (field: string, value: boolean) => {
    setNotifications(prev => ({ ...prev, [field]: value }));
  };

  const handlePasswordInputChange = (field: string, value: string) => {
    setPasswordData(prev => ({ ...prev, [field]: value }));
  };

  const togglePasswordVisibility = (field: string) => {
    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }));
  };

  const validatePasswordChange = () => {
    if (passwordData.newPassword.length < 6) {
      return "New password must be at least 6 characters long";
    }
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      return "New passwords do not match";
    }
    if (!passwordData.currentPassword) {
      return "Current password is required";
    }
    return null;
  };

  const handlePasswordChange = async () => {
    const validationError = validatePasswordChange();
    if (validationError) {
      toast({
        title: "Validation Error",
        description: validationError,
        variant: "destructive",
      });
      return;
    }

    setPasswordLoading(true);
    try {
      // First verify the current password by attempting to sign in
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user?.email || "",
        password: passwordData.currentPassword,
      });

      if (signInError) {
        throw new Error("Current password is incorrect");
      }

      // Update the password
      const { error: updateError } = await supabase.auth.updateUser({
        password: passwordData.newPassword
      });

      if (updateError) {
        throw updateError;
      }

      // Clear the form and hide it
      setPasswordData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: ""
      });
      setShowPasswordForm(false);

      toast({
        title: "Password Updated",
        description: "Your password has been successfully updated.",
      });
    } catch (error: any) {
      toast({
        title: "Password Update Failed",
        description: error.message || "Failed to update password. Please try again.",
        variant: "destructive",
      });
    } finally {
      setPasswordLoading(false);
    }
  };



  const handleSaveProfile = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const { error } = await supabase.auth.updateUser({
        data: {
          first_name: formData.firstName,
          last_name: formData.lastName,
          business_name: formData.businessName,
          timezone: formData.timezone
        }
      });

      if (error) throw error;

      toast({
        title: "Profile Updated",
        description: "Your profile information has been successfully updated.",
      });
    } catch (error: any) {
      toast({
        title: "Update Failed",
        description: error.message || "Failed to update profile. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAccount = async () => {
    if (!user) return;

    setDeleteLoading(true);
    try {
      // Mark the account as disabled in user metadata
      const { error: updateError } = await supabase.auth.updateUser({
        data: {
          account_status: 'disabled',
          disabled_at: new Date().toISOString(),
          // Keep existing metadata but mark as disabled
          ...user.user_metadata,
        }
      });

      if (updateError) {
        throw updateError;
      }

      toast({
        title: "Account Disabled",
        description: "Your account has been disabled. You will now be signed out.",
      });

      // Sign out the user
      await signOut();

      // Redirect to landing page
      navigate('/');

    } catch (error: any) {
      toast({
        title: "Account Deletion Failed",
        description: error.message || "Failed to disable account. Please try again.",
        variant: "destructive",
      });
    } finally {
      setDeleteLoading(false);
      setShowDeleteConfirm(false);
    }
  };

  const settingsMenuItems = [
    { icon: User, label: t('profile'), id: "profile", color: "text-blue-600", bgColor: "bg-blue-50 hover:bg-blue-100" },
    { icon: Bell, label: t('notifications'), id: "notifications", color: "text-purple-600", bgColor: "bg-purple-50 hover:bg-purple-100" },
    { icon: Key, label: t('apiKeys'), id: "api", color: "text-green-600", bgColor: "bg-green-50 hover:bg-green-100" },
    { icon: CreditCard, label: t('billing'), id: "billing", color: "text-orange-600", bgColor: "bg-orange-50 hover:bg-orange-100" },
    { icon: Shield, label: t('security'), id: "security", color: "text-red-600", bgColor: "bg-red-50 hover:bg-red-100" }
  ];

  return (
    <div className="space-y-6" dir={direction}>
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-foreground">{t('settings')}</h1>
        <p className="text-muted-foreground mt-1">{t('manageAccountPreferences')}</p>
      </div>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* Settings Navigation */}
        <Card className="lg:col-span-1 bg-white shadow-lg border-0">
          <CardHeader className="bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200">
            <CardTitle className="flex items-center gap-3 text-slate-800">
              <div className="p-2 bg-gradient-to-br from-teal-500 to-blue-600 rounded-xl">
                <SettingsIcon className="w-5 h-5 text-white" />
              </div>
              {t('settingsMenu')}
            </CardTitle>
          </CardHeader>
          <CardContent className="p-2">
            <div className="space-y-2">
              {settingsMenuItems.map((item) => (
                <div
                  key={item.id}
                  onClick={() => setActiveTab(item.id)}
                  className={`flex items-center gap-3 px-4 py-3 mx-2 rounded-lg cursor-pointer transition-all duration-200 w-full ${
                    activeTab === item.id
                      ? 'bg-gradient-to-r from-teal-500 to-blue-600 text-white shadow-lg'
                      : 'text-gray-700 hover:bg-white hover:shadow-md'
                  }`}
                >
                  <div className={`p-2 rounded-lg flex-shrink-0 ${
                    activeTab === item.id
                      ? "bg-white/20"
                      : item.bgColor
                  }`}>
                    <item.icon className={`w-4 h-4 ${
                      activeTab === item.id ? "text-white" : item.color
                    }`} />
                  </div>
                  <span className={`font-medium text-sm flex-1 ${
                    activeTab === item.id ? "text-white" : "text-gray-700"
                  }`}>
                    {item.label}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Settings Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Profile Settings */}
          {activeTab === "profile" && (
            <Card>
              <CardHeader>
                <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <User className="w-5 h-5" />
                  {t('profileInformation')}
                </CardTitle>
                <CardDescription>{t('updatePersonalInfo')}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid sm:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="firstName">{t('firstName')}</Label>
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange("firstName", e.target.value)}
                      placeholder={t('enterFirstName')}
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName">{t('lastName')}</Label>
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange("lastName", e.target.value)}
                      placeholder={t('enterLastName')}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="email">{t('emailAddress')}</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    disabled
                    className="bg-gray-50"
                  />
                  <p className="text-xs text-gray-500 mt-1">{t('emailCannotChange')}</p>
                </div>
                <div>
                  <Label htmlFor="business">{t('businessName')}</Label>
                  <Input
                    id="business"
                    value={formData.businessName}
                    onChange={(e) => handleInputChange("businessName", e.target.value)}
                    placeholder={t('enterBusinessName')}
                  />
                </div>
                <div>
                  <Label htmlFor="timezone">{t('timezone')}</Label>
                  <Select value={formData.timezone} onValueChange={(value) => handleInputChange("timezone", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('selectTimezone')} />
                    </SelectTrigger>
                    <SelectContent className="bg-white">
                      <SelectItem value="est">{t('easternTime')}</SelectItem>
                      <SelectItem value="cst">{t('centralTime')}</SelectItem>
                      <SelectItem value="mst">{t('mountainTime')}</SelectItem>
                      <SelectItem value="pst">{t('pacificTime')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button onClick={handleSaveProfile} disabled={loading}>
                  {loading ? t('saving') : t('saveChanges')}
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Notification Settings */}
          {activeTab === "notifications" && (
            <Card>
              <CardHeader>
                <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <Bell className="w-5 h-5" />
                  {t('notificationPreferences')}
                </CardTitle>
                <CardDescription>{t('chooseNotifications')}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div>
                    <p className="font-medium">{t('newMessages')}</p>
                    <p className="text-sm text-gray-600">{t('newMessagesDesc')}</p>
                  </div>
                  <Switch
                    checked={notifications.newMessages}
                    onCheckedChange={(checked) => handleNotificationChange("newMessages", checked)}
                  />
                </div>
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div>
                    <p className="font-medium">{t('botPerformanceAlerts')}</p>
                    <p className="text-sm text-gray-600">{t('botPerformanceDesc')}</p>
                  </div>
                  <Switch
                    checked={notifications.botPerformance}
                    onCheckedChange={(checked) => handleNotificationChange("botPerformance", checked)}
                  />
                </div>
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div>
                    <p className="font-medium">{t('usageWarnings')}</p>
                    <p className="text-sm text-gray-600">{t('usageWarningsDesc')}</p>
                  </div>
                  <Switch
                    checked={notifications.usageWarnings}
                    onCheckedChange={(checked) => handleNotificationChange("usageWarnings", checked)}
                  />
                </div>
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div>
                    <p className="font-medium">{t('weeklyReports')}</p>
                    <p className="text-sm text-gray-600">{t('weeklyReportsDesc')}</p>
                  </div>
                  <Switch
                    checked={notifications.weeklyReports}
                    onCheckedChange={(checked) => handleNotificationChange("weeklyReports", checked)}
                  />
                </div>
                <Button
                  onClick={() => {
                    toast({
                      title: t('notificationPrefsUpdated'),
                      description: t('notificationPrefsSaved'),
                    });
                  }}
                  className="mt-4"
                >
                  {t('saveNotificationSettings')}
                </Button>
              </CardContent>
            </Card>
          )}

          {/* API Keys */}
          {activeTab === "api" && (
            <Card>
              <CardHeader>
                <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <Key className="w-5 h-5" />
                  {t('apiKeys')}
                </CardTitle>
                <CardDescription>{t('manageApiKeys')}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className={`flex items-center justify-between p-3 border rounded-lg ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div>
                    <p className="font-medium">{t('whatsappBusinessApi')}</p>
                    <p className="text-sm text-gray-600">sk-...d4f2</p>
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">{t('active')}</Badge>
                </div>
                <div className={`flex items-center justify-between p-3 border rounded-lg ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div>
                    <p className="font-medium">{t('facebookMessenger')}</p>
                    <p className="text-sm text-gray-600">{t('notConnected')}</p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      toast({
                        title: t('comingSoon'),
                        description: t('facebookIntegrationSoon'),
                      });
                    }}
                  >
                    {t('connect')}
                  </Button>
                </div>
                <div className={`flex items-center justify-between p-3 border rounded-lg ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div>
                    <p className="font-medium">{t('smsGateway')}</p>
                    <p className="text-sm text-gray-600">{t('notConnected')}</p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      toast({
                        title: t('comingSoon'),
                        description: t('smsIntegrationSoon'),
                      });
                    }}
                  >
                    {t('connect')}
                  </Button>
                </div>
                <Button
                  variant="outline"
                  onClick={() => {
                    toast({
                      title: t('apiKeyGenerated'),
                      description: t('apiKeyGeneratedDesc'),
                    });
                  }}
                >
                  {t('generateNewApiKey')}
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Billing */}
          {activeTab === "billing" && (
            <Card>
              <CardHeader>
                <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <CreditCard className="w-5 h-5" />
                  {t('billingSubscription')}
                </CardTitle>
                <CardDescription>{t('manageBillingInfo')}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 border rounded-lg bg-blue-50">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-blue-800">Current Plan: Free</h4>
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">Active</Badge>
                  </div>
                  <p className="text-sm text-blue-600 mb-3">
                    You're currently on the free plan with 500 messages per month.
                  </p>
                  <Button
                    onClick={() => {
                      toast({
                        title: "Redirecting to Pricing",
                        description: "Taking you to our pricing page to upgrade your plan.",
                      });
                    }}
                  >
                    Upgrade Plan
                  </Button>
                </div>
                <div className="space-y-3">
                  <h4 className="font-medium">Usage This Month</h4>
                  <div className="flex justify-between text-sm">
                    <span>Messages Used</span>
                    <span>245 / 500</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: '49%' }}></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Security */}
          {activeTab === "security" && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  Security Settings
                </CardTitle>
                <CardDescription>Manage your account security and privacy</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Password Change Section */}
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Password Security</h4>
                    <p className="text-sm text-gray-600 mb-4">Keep your account secure by updating your password</p>
                  </div>

                  {!showPasswordForm ? (
                    <Button
                      onClick={() => setShowPasswordForm(true)}
                      variant="outline"
                    >
                      Change Password
                    </Button>
                  ) : (
                    <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
                      <div className="flex items-center justify-between mb-4">
                        <h5 className="font-medium">Update Your Password</h5>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setShowPasswordForm(false);
                            setPasswordData({
                              currentPassword: "",
                              newPassword: "",
                              confirmPassword: ""
                            });
                          }}
                        >
                          Cancel
                        </Button>
                      </div>
                      <div>
                        <Label htmlFor="currentPassword">Current Password</Label>
                        <div className="relative">
                          <Input
                            id="currentPassword"
                            type={showPasswords.current ? "text" : "password"}
                            placeholder="Enter your current password"
                            value={passwordData.currentPassword}
                            onChange={(e) => handlePasswordInputChange("currentPassword", e.target.value)}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => togglePasswordVisibility("current")}
                          >
                            {showPasswords.current ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="newPassword">New Password</Label>
                        <div className="relative">
                          <Input
                            id="newPassword"
                            type={showPasswords.new ? "text" : "password"}
                            placeholder="Enter your new password"
                            value={passwordData.newPassword}
                            onChange={(e) => handlePasswordInputChange("newPassword", e.target.value)}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => togglePasswordVisibility("new")}
                          >
                            {showPasswords.new ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          Password must be at least 6 characters long
                        </p>
                      </div>

                      <div>
                        <Label htmlFor="confirmPassword">Confirm New Password</Label>
                        <div className="relative">
                          <Input
                            id="confirmPassword"
                            type={showPasswords.confirm ? "text" : "password"}
                            placeholder="Confirm your new password"
                            value={passwordData.confirmPassword}
                            onChange={(e) => handlePasswordInputChange("confirmPassword", e.target.value)}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => togglePasswordVisibility("confirm")}
                          >
                            {showPasswords.confirm ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>

                      <Button
                        onClick={handlePasswordChange}
                        disabled={passwordLoading}
                        className="w-full"
                      >
                        {passwordLoading ? "Updating Password..." : "Update Password"}
                      </Button>
                    </div>
                  )}
                </div>

                {/* Danger Zone */}
                <div className="border-t pt-6">
                  <h4 className="font-medium mb-2 text-red-600">Danger Zone</h4>
                  <div className="p-4 border border-red-200 rounded-lg bg-red-50">
                    <h5 className="font-medium text-red-800 mb-2">Disable Account</h5>
                    <p className="text-sm text-red-600 mb-3">
                      Permanently disable your account and prevent future access. This action cannot be undone.
                    </p>

                    {!showDeleteConfirm ? (
                      <Button
                        variant="destructive"
                        onClick={() => setShowDeleteConfirm(true)}
                      >
                        Disable Account
                      </Button>
                    ) : (
                      <div className="space-y-3">
                        <div className="p-3 bg-red-100 border border-red-300 rounded">
                          <p className="text-sm text-red-800 font-medium mb-2">
                            ⚠️ Are you absolutely sure?
                          </p>
                          <p className="text-xs text-red-700">
                            This will permanently disable your account. You will not be able to sign in again.
                          </p>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="destructive"
                            onClick={handleDeleteAccount}
                            disabled={deleteLoading}
                            size="sm"
                          >
                            {deleteLoading ? "Disabling..." : "Yes, Disable My Account"}
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => setShowDeleteConfirm(false)}
                            size="sm"
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default Settings;
