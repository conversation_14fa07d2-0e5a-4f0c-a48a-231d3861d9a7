
import { Message<PERSON>quare, Filter, Search, User, Bo<PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useMessages } from "@/hooks/useMessages";
import { useBots } from "@/hooks/useBots";
import { useState } from "react";

const Messages = () => {
  const [selectedBot, setSelectedBot] = useState<string>('all');
  const [selectedMessage, setSelectedMessage] = useState<string>('');
  
  const { messages, loading: messagesLoading } = useMessages(selectedBot);
  const { bots, loading: botsLoading } = useBots();

  if (messagesLoading || botsLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Group messages by conversation/bot
  const conversationGroups = messages.reduce((acc, message) => {
    const key = message.bot_id;
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(message);
    return acc;
  }, {} as Record<string, typeof messages>);

  const conversations = Object.entries(conversationGroups).map(([botId, msgs]) => {
    const bot = bots.find(b => b.id === botId);
    const lastMessage = msgs[0]; // Already sorted by timestamp desc
    return {
      id: botId,
      bot: bot?.name || 'Unknown Bot',
      channel: bot?.channel || 'unknown',
      lastMessage: lastMessage.content,
      timestamp: new Date(lastMessage.timestamp).toLocaleString(),
      status: msgs.some(m => m.sender === 'user') ? 'active' : 'resolved',
      messageCount: msgs.length
    };
  });

  const selectedConversation = selectedBot !== 'all' ? conversationGroups[selectedBot] || [] : [];
  const selectedBotInfo = selectedBot !== 'all' ? bots.find(b => b.id === selectedBot) : null;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Messages</h1>
          <p className="text-gray-600 mt-1">View and manage bot conversations</p>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search conversations..."
                className="w-full pl-10"
              />
            </div>
            <Select onValueChange={setSelectedBot}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="All Bots" />
              </SelectTrigger>
              <SelectContent className="bg-white">
                <SelectItem value="all">All Bots</SelectItem>
                {bots.map((bot) => (
                  <SelectItem key={bot.id} value={bot.id}>{bot.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="All Channels" />
              </SelectTrigger>
              <SelectContent className="bg-white">
                <SelectItem value="all">All Channels</SelectItem>
                <SelectItem value="whatsapp">WhatsApp</SelectItem>
                <SelectItem value="facebook">Facebook</SelectItem>
                <SelectItem value="sms">SMS</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* Conversations List */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="w-5 h-5" />
              Recent Conversations
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {conversations.length === 0 ? (
              <div className="p-8 text-center text-muted-foreground">
                <MessageSquare className="w-12 h-12 mx-auto mb-3 opacity-50" />
                <p>No conversations yet</p>
                <p className="text-sm">Messages will appear here once your bots start chatting!</p>
              </div>
            ) : (
              <div className="space-y-1">
                {conversations.map((conv) => (
                  <div
                    key={conv.id}
                    onClick={() => setSelectedBot(conv.id)}
                    className={`p-4 hover:bg-gray-50 cursor-pointer border-l-4 transition-colors ${
                      selectedBot === conv.id ? 'border-l-teal-500 bg-teal-50' : 'border-l-transparent'
                    }`}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-medium text-gray-900">{conv.bot}</h4>
                      <Badge
                        variant={conv.status === 'active' ? 'default' : 'secondary'}
                        className={conv.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                      >
                        {conv.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-500 truncate">{conv.lastMessage}</p>
                    <div className="flex justify-between items-center mt-2">
                      <Badge variant="outline" className="text-xs">
                        {conv.channel}
                      </Badge>
                      <span className="text-xs text-gray-400">{conv.timestamp}</span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Message Thread */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5" />
              {selectedBotInfo ? `${selectedBotInfo.name} Messages` : 'Select a conversation'}
            </CardTitle>
            {selectedBotInfo && (
              <div className="flex gap-2">
                <Badge variant="outline">{selectedBotInfo.channel}</Badge>
                <Badge variant="secondary">Active</Badge>
              </div>
            )}
          </CardHeader>
          <CardContent>
            {selectedConversation.length === 0 ? (
              <div className="h-96 flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <MessageSquare className="w-16 h-16 mx-auto mb-4 opacity-30" />
                  <p className="text-lg font-medium">No conversation selected</p>
                  <p className="text-sm">Select a conversation from the left to view messages</p>
                </div>
              </div>
            ) : (
              <>
                <div className="space-y-4 h-96 overflow-y-auto mb-4">
                  {selectedConversation.reverse().map((message, index) => (
                    <div
                      key={message.id}
                      className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                          message.sender === 'user'
                            ? 'bg-teal-500 text-white'
                            : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        <div className="flex items-center gap-2 mb-1">
                          {message.sender === 'user' ? (
                            <User className="w-3 h-3" />
                          ) : (
                            <Bot className="w-3 h-3" />
                          )}
                          <span className="text-xs opacity-75">
                            {new Date(message.timestamp).toLocaleTimeString()}
                          </span>
                        </div>
                        <p className="text-sm">{message.content}</p>
                      </div>
                    </div>
                  ))}
                </div>
                
                {/* Quick Actions */}
                <div className="flex gap-2 pt-4 border-t">
                  <Button variant="outline" size="sm">Mark as Resolved</Button>
                  <Button variant="outline" size="sm">Transfer to Human</Button>
                  <Button variant="outline" size="sm">Export Chat</Button>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Messages;
