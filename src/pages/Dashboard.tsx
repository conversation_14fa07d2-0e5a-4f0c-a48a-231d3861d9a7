
import { <PERSON><PERSON>, MessageSquare, Users, FileText, MoreHorizontal, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useNavigate } from "react-router-dom";

import { useBots } from "@/hooks/useBots";
import { useMessages } from "@/hooks/useMessages";
import { useUserProfile } from "@/hooks/useUserProfile";

import { useLanguage } from "@/contexts/LanguageContext";

const Dashboard = () => {
  const navigate = useNavigate();
  const { t, isRTL, direction } = useLanguage();
  const { bots, loading: botsLoading, deleteBot } = useBots();
  const { messages, loading: messagesLoading } = useMessages();
  const { profile, loading: profileLoading } = useUserProfile();

  const handleCreateBot = () => {
    navigate('/create-bot');
  };

  const handleEditBot = (botId: string) => {
    navigate(`/bot/${botId}/configure`);
  };

  const handleViewAnalytics = (_botId: string) => {
    navigate('/analytics');
  };

  const handleTestBot = (botId: string) => {
    navigate(`/bot/${botId}/test`);
  };

  const handleDeleteBot = async (botId: string) => {
    await deleteBot(botId);
  };

  if (botsLoading || messagesLoading || profileLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-teal-500 mx-auto mb-4"></div>
          <p className="text-slate-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  const getStatGradient = (index: number) => {
    const gradients = [
      "from-blue-500 to-blue-600",
      "from-emerald-500 to-emerald-600",
      "from-purple-500 to-purple-600",
      "from-orange-500 to-orange-600"
    ];
    return gradients[index % gradients.length];
  };

  const getBotGradient = (index: number) => {
    const gradients = [
      "from-teal-500 to-blue-600",
      "from-purple-500 to-pink-600",
      "from-orange-500 to-red-600",
      "from-green-500 to-teal-600",
      "from-indigo-500 to-purple-600"
    ];
    return gradients[index % gradients.length];
  };

  // Remove the mock bots array since we're now using real data from the database

  const stats = [
    {
      title: t('totalMessages'),
      value: messages.length.toString(),
      change: "+12%",
      icon: MessageSquare,
      color: "text-blue-600"
    },
    {
      title: t('activeUsers'),
      value: "157", // This would come from a separate analytics query
      change: "+8%",
      icon: Users,
      color: "text-green-600"
    },
    {
      title: t('activeBots'),
      value: bots.filter(bot => bot.status === 'active').length.toString(),
      change: "0%",
      icon: Bot,
      color: "text-purple-600"
    },
    {
      title: t('tokensUsed'),
      value: profile?.tokens_used?.toString() || "0",
      change: `${t(profile?.plan === 'pro' ? 'pro' : profile?.plan === 'business' ? 'enterprise' : 'free')} plan`,
      icon: FileText,
      color: "text-orange-600"
    }
  ];

  return (
    <div className={`space-y-8 ${isRTL ? 'dashboard-grid-rtl' : ''}`} dir={direction}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 animate-fade-in">
        <div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
            {t('dashboard')}
          </h1>
          <p className="text-slate-600 mt-2 text-lg">{t('manageBots')}</p>
        </div>
        <Button
          onClick={handleCreateBot}
          className="bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 px-6 py-3 rounded-xl font-semibold"
        >
          <Plus className={`w-5 h-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
          {t('createBot')}
        </Button>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index} className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 rounded-2xl overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-semibold text-slate-500 uppercase tracking-wider">{stat.title}</p>
                  <p className="text-3xl font-bold text-slate-800 mt-2">{stat.value}</p>
                  <p className="text-sm text-emerald-600 mt-2 font-semibold bg-emerald-50 px-2 py-1 rounded-full inline-block">
                    {stat.change} {t('fromLastMonth')}
                  </p>
                </div>
                <div className={`p-4 rounded-2xl bg-gradient-to-br ${getStatGradient(index)} shadow-lg`}>
                  <stat.icon className="w-7 h-7 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Bots Overview */}
      <Card className="bg-white border-0 shadow-lg rounded-2xl overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200">
          <CardTitle className="flex items-center gap-3 text-slate-800">
            <div className="p-2 bg-gradient-to-br from-teal-500 to-blue-600 rounded-xl">
              <Bot className="w-5 h-5 text-white" />
            </div>
            {t('myBots')}
          </CardTitle>
          <CardDescription className="text-slate-600 text-base">
            {t('manageBots')}
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-4">
            {bots.length === 0 ? (
              <div className="text-center py-12">
                <Bot className="w-16 h-16 text-slate-300 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-slate-600 mb-2">{t('noBots')}</h3>
                <p className="text-slate-500 mb-6">{t('createFirstBot')}</p>
                <Button
                  onClick={handleCreateBot}
                  className="bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white shadow-lg"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  {t('createBot')}
                </Button>
              </div>
            ) : (
              bots.map((bot, index) => (
                <div key={bot.id} className="flex items-center justify-between p-5 border border-slate-200 rounded-2xl hover:bg-gradient-to-r hover:from-slate-50 hover:to-slate-100 transition-all duration-300 hover:shadow-md hover:scale-[1.02] group">
                  <div className="flex items-center gap-4">
                    <div className={`w-14 h-14 bg-gradient-to-br ${getBotGradient(index)} rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                      <Bot className="w-7 h-7 text-white" />
                    </div>
                    <div>
                      <h3 className="font-bold text-slate-800 text-lg">{bot.name}</h3>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge className="text-xs font-semibold bg-gradient-to-r from-teal-100 to-blue-100 text-teal-700 border-0 hover:from-teal-200 hover:to-blue-200 transition-colors">
                          {bot.channel}
                        </Badge>
                        <span className="text-xs text-slate-500">
                          Created {new Date(bot.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-6">
                    <div className="text-center hidden sm:block bg-slate-50 px-3 py-2 rounded-xl">
                      <p className="text-lg font-bold text-slate-800">
                        {messages.filter(msg => msg.bot_id === bot.id).length}
                      </p>
                      <p className="text-xs text-slate-500 font-medium">{t('messages')}</p>
                    </div>
                    <div className="flex items-center gap-3">
                      <Badge
                        className={`font-semibold px-3 py-1 ${
                          bot.status === 'active'
                            ? 'bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-700 border-emerald-200'
                            : 'bg-gradient-to-r from-gray-100 to-slate-100 text-slate-600 border-slate-200'
                        }`}
                      >
                        {bot.status}
                      </Badge>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="hover:bg-slate-100 rounded-xl transition-all duration-200 hover:scale-105">
                            <MoreHorizontal className="w-4 h-4 text-slate-600" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="bg-white border border-slate-200 shadow-xl rounded-xl p-2">
                          <DropdownMenuItem
                            className="hover:bg-accent cursor-pointer"
                            onClick={() => handleEditBot(bot.id)}
                          >
                            Edit Bot
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="hover:bg-accent cursor-pointer"
                            onClick={() => handleViewAnalytics(bot.id)}
                          >
                            View Analytics
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="hover:bg-accent cursor-pointer"
                            onClick={() => handleTestBot(bot.id)}
                          >
                            Test Bot
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-destructive hover:bg-destructive/10 cursor-pointer"
                            onClick={() => handleDeleteBot(bot.id)}
                          >
                            Delete Bot
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Dashboard;
