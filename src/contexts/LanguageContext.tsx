import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

// Define supported languages
export const SUPPORTED_LANGUAGES = {
  en: { code: 'en', name: 'English', flag: '🇺🇸', rtl: false },
  es: { code: 'es', name: '<PERSON>spañol', flag: '🇪🇸', rtl: false },
  fr: { code: 'fr', name: 'Français', flag: '🇫🇷', rtl: false },
  de: { code: 'de', name: 'Deuts<PERSON>', flag: '🇩🇪', rtl: false },
  it: { code: 'it', name: 'Italiano', flag: '🇮🇹', rtl: false },
  pt: { code: 'pt', name: 'Português', flag: '🇵🇹', rtl: false },
  zh: { code: 'zh', name: '中文', flag: '🇨🇳', rtl: false },
  ja: { code: 'ja', name: '日本語', flag: '🇯🇵', rtl: false },
  ko: { code: 'ko', name: '한국어', flag: '🇰🇷', rtl: false },
  ar: { code: 'ar', name: 'العربية', flag: '🇸🇦', rtl: true },
} as const;

export type LanguageCode = keyof typeof SUPPORTED_LANGUAGES;

// Basic translations - you can expand this
const translations = {
  en: {
    // Navigation
    navigation: 'Navigation',
    dashboard: 'Dashboard',
    botTesting: 'Bot Testing',
    messages: 'Messages',
    analytics: 'Analytics',
    settings: 'Settings',
    pricing: 'Pricing',
    profile: 'Profile',
    notifications: 'Notifications',
    security: 'Security',
    billing: 'Billing',
    apiKeys: 'API Keys',
    signOut: 'Sign Out',
    language: 'Language',
    selectLanguage: 'Select Language',

    // Common actions
    save: 'Save',
    cancel: 'Cancel',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    edit: 'Edit',
    delete: 'Delete',
    create: 'Create',
    update: 'Update',
    confirm: 'Confirm',
    close: 'Close',

    // Landing page
    heroTitle1: 'Chat,',
    heroTitle2: 'Create,',
    heroTitle3: 'Deploy',
    heroSubtitle: 'Build intelligent chatbots for WhatsApp, SMS, and Messenger in minutes. No coding required.',
    getStarted: 'Get Started Free',
    signIn: 'Sign In',
    trustedBy: 'Trusted by 500+ small businesses worldwide',
    instantSetup: 'Instant Setup',
    instantSetupDesc: 'Get your chatbot ready in minutes, not hours',
    multiPlatform: 'Multi-Platform',
    multiPlatformDesc: 'Works on WhatsApp, Facebook, and SMS',
    securePrivate: 'Secure & Private',
    securePrivateDesc: 'Your data stays protected and confidential',

    // Dashboard
    myBots: 'My Bots',
    createBot: 'Create New Bot',
    noBots: 'No bots created yet',
    createFirstBot: 'Create your first chatbot to get started',
    recentMessages: 'Recent Messages',
    noMessages: 'No messages yet',
    startConversation: 'Start a conversation with your bots to see messages here',
    manageBots: 'Manage and monitor your active chatbots',
    totalMessages: 'Total Messages',
    activeUsers: 'Active Users',
    activeBots: 'Active Bots',
    tokensUsed: 'Tokens Used',
    fromLastMonth: 'from last month',
    created: 'Created',
    editBot: 'Edit Bot',
    viewAnalytics: 'View Analytics',
    testBot: 'Test Bot',
    deleteBot: 'Delete Bot',

    // Settings
    profileInformation: 'Profile Information',
    updatePersonalInfo: 'Update your personal information and business details',
    firstName: 'First Name',
    lastName: 'Last Name',
    emailAddress: 'Email Address',
    businessName: 'Business Name',
    timezone: 'Timezone',
    emailCannotChange: 'Email cannot be changed here. Contact support if needed.',
    notificationPreferences: 'Notification Preferences',
    chooseNotifications: 'Choose what notifications you want to receive',
    newMessages: 'New Messages',
    newMessagesDesc: 'Get notified when users send messages to your bots',
    botPerformanceAlerts: 'Bot Performance Alerts',
    botPerformanceDesc: 'Receive alerts about bot performance issues',
    usageWarnings: 'Usage Warnings',
    usageWarningsDesc: 'Get notified when approaching token limits',
    weeklyReports: 'Weekly Reports',
    weeklyReportsDesc: 'Receive weekly performance summaries',
    saveNotificationSettings: 'Save Notification Settings',

    // Notifications/Toasts
    languageChanged: 'Language changed to',
    failedToChangeLanguage: 'Failed to change language. Please try again.',
    profileUpdated: 'Profile Updated',
    profileUpdateSuccess: 'Your profile information has been successfully updated.',
    updateFailed: 'Update Failed',
    updateFailedDesc: 'Failed to update profile. Please try again.',
    notificationPrefsUpdated: 'Notification Preferences Updated',
    notificationPrefsSaved: 'Your notification settings have been saved.',

    // Help & Support
    helpSupport: 'Help & Support',
    contactSupport: 'Contact <NAME_EMAIL> for assistance',

    // Analytics page
    trackPerformance: 'Track your chatbot performance and insights',
    exportReport: 'Export Report',
    last7Days: 'Last 7 days',
    last30Days: 'Last 30 days',
    last90Days: 'Last 90 days',
    lastYear: 'Last year',
    totalConversations: 'Total Conversations',
    activeUsers: 'Active Users',
    avgResponseTime: 'Avg Response Time',
    resolutionRate: 'Resolution Rate',
    vsLastPeriod: 'vs last period',
    messagesUsersOverTime: 'Messages & Users Over Time',
    monthlyTrends: 'Monthly conversation and user trends',
    channelDistribution: 'Channel Distribution',
    messagesByChannel: 'Messages by communication channel',
    botPerformance: 'Bot Performance',
    individualBotStats: 'Individual chatbot statistics and metrics',
    totalMessages: 'total messages',
    satisfaction: 'Satisfaction',
    avgResponse: 'Avg Response',
    active: 'Active',

    // Messages page
    viewManageConversations: 'View and manage bot conversations',
    searchConversations: 'Search conversations...',
    allBots: 'All Bots',
    allChannels: 'All Channels',
    recentConversations: 'Recent Conversations',
    noConversationsYet: 'No conversations yet',
    messagesWillAppear: 'Messages will appear here once your bots start chatting!',
    selectConversation: 'Select a conversation',
    noConversationSelected: 'No conversation selected',
    selectConversationLeft: 'Select a conversation from the left to view messages',
    markAsResolved: 'Mark as Resolved',
    transferToHuman: 'Transfer to Human',
    exportChat: 'Export Chat',

    // Settings page
    manageAccountPreferences: 'Manage your account and chatbot preferences',
    settingsMenu: 'Settings Menu',
    profileInformation: 'Profile Information',
    updatePersonalInfo: 'Update your personal information and business details',
    firstName: 'First Name',
    lastName: 'Last Name',
    emailAddress: 'Email Address',
    businessName: 'Business Name',
    timezone: 'Timezone',
    emailCannotChange: 'Email cannot be changed here. Contact support if needed.',
    enterFirstName: 'Enter your first name',
    enterLastName: 'Enter your last name',
    enterBusinessName: 'Enter your business name',
    selectTimezone: 'Select timezone',
    easternTime: 'Eastern Time (ET)',
    centralTime: 'Central Time (CT)',
    mountainTime: 'Mountain Time (MT)',
    pacificTime: 'Pacific Time (PT)',
    saving: 'Saving...',
    saveChanges: 'Save Changes',

    // Notification settings
    notificationPreferences: 'Notification Preferences',
    chooseNotifications: 'Choose what notifications you want to receive',
    newMessages: 'New Messages',
    newMessagesDesc: 'Get notified when users send messages to your bots',
    botPerformanceAlerts: 'Bot Performance Alerts',
    botPerformanceDesc: 'Receive alerts about bot performance issues',
    usageWarnings: 'Usage Warnings',
    usageWarningsDesc: 'Get notified when approaching token limits',
    weeklyReports: 'Weekly Reports',
    weeklyReportsDesc: 'Receive weekly performance summaries',
    saveNotificationSettings: 'Save Notification Settings',
    notificationPrefsUpdated: 'Notification Preferences Updated',
    notificationPrefsSaved: 'Your notification settings have been saved.',

    // API Keys
    apiKeys: 'API Keys',
    manageApiKeys: 'Manage your API keys for integrations',
    whatsappBusinessApi: 'WhatsApp Business API',
    facebookMessenger: 'Facebook Messenger',
    smsGateway: 'SMS Gateway',
    notConnected: 'Not connected',
    connect: 'Connect',
    comingSoon: 'Coming Soon',
    facebookIntegrationSoon: 'Facebook Messenger integration will be available soon.',
    smsIntegrationSoon: 'SMS Gateway integration will be available soon.',
    generateNewApiKey: 'Generate New API Key',
    apiKeyGenerated: 'API Key Generated',
    apiKeyGeneratedDesc: 'A new API key has been generated and sent to your email.',

    // Billing
    billingSubscription: 'Billing & Subscription',
    manageBillingInfo: 'Manage your subscription and billing information',
    currentPlan: 'Current Plan',
    currentPlanFree: 'You\'re currently on the free plan with 500 messages per month.',
    upgradePlan: 'Upgrade Plan',
    redirectingToPricing: 'Redirecting to Pricing',
    takingToPricing: 'Taking you to our pricing page to upgrade your plan.',
    usageThisMonth: 'Usage This Month',
    messagesUsed: 'Messages Used',

    // Security
    securitySettings: 'Security Settings',
    manageAccountSecurity: 'Manage your account security and privacy',
    passwordSecurity: 'Password Security',
    keepAccountSecure: 'Keep your account secure by updating your password',
    changePassword: 'Change Password',
    updateYourPassword: 'Update Your Password',
    currentPassword: 'Current Password',
    newPassword: 'New Password',
    confirmNewPassword: 'Confirm New Password',
    enterCurrentPassword: 'Enter your current password',
    enterNewPassword: 'Enter your new password',
    confirmNewPasswordPlaceholder: 'Confirm your new password',
    passwordMinLength: 'Password must be at least 6 characters long',
    updatingPassword: 'Updating Password...',
    updatePassword: 'Update Password',
    dangerZone: 'Danger Zone',
    disableAccount: 'Disable Account',
    disableAccountDesc: 'Permanently disable your account and prevent future access. This action cannot be undone.',
    areYouSure: 'Are you absolutely sure?',
    disableAccountWarning: 'This will permanently disable your account. You will not be able to sign in again.',
    disabling: 'Disabling...',
    yesDisableAccount: 'Yes, Disable My Account',

    // Validation messages
    validationError: 'Validation Error',
    passwordTooShort: 'New password must be at least 6 characters long',
    passwordsDoNotMatch: 'New passwords do not match',
    currentPasswordRequired: 'Current password is required',
    passwordUpdated: 'Password Updated',
    passwordUpdateSuccess: 'Your password has been successfully updated.',
    passwordUpdateFailed: 'Password Update Failed',
    currentPasswordIncorrect: 'Current password is incorrect',
    accountDisabled: 'Account Disabled',
    accountDisabledDesc: 'Your account has been disabled. You will now be signed out.',
    accountDeletionFailed: 'Account Deletion Failed',
    failedToDisableAccount: 'Failed to disable account. Please try again.',

    // ChatInterface
    chatbotGreeting: 'Hi there! 👋 Tell me about your business — what do you do, what services do you offer, and what should your chatbot know?',
    typeMessage: 'Type your message...',
    attachFile: 'Attach file',
    sendMessage: 'Send message',
    uploadFiles: 'Upload Files',
    dragDropFiles: 'Drag and drop files here, or click to select',
    supportedFormats: 'Supported formats: PDF, TXT, DOC, DOCX (max 10MB each)',
    filesUploaded: 'files uploaded',
    removeFile: 'Remove file',
    createChatbot: 'Create Chatbot',
    creatingBot: 'Creating your bot...',
    chatbotCreated: 'Chatbot Created!',
    chatbotCreatedDesc: 'Your chatbot has been created successfully.',
    chatbotCreationFailed: 'Chatbot Creation Failed',
    failedToCreateBot: 'Failed to create chatbot. Please try again.',
    fileUploadFailed: 'File Upload Failed',
    fileTooLarge: 'File is too large. Maximum size is 10MB.',
    unsupportedFileType: 'Unsupported file type. Please use PDF, TXT, DOC, or DOCX.',
    uploadError: 'Error uploading file. Please try again.',
    thinkingResponse: 'Thinking...',

    // BotTesting
    testYourChatbot: 'Test Your Chatbot',
    simulateConversations: 'Simulate conversations across different channels',
    botGreeting: 'Hello! I\'m your Restaurant Assistant. How can I help you today?',
    testConversation: 'Test Conversation',
    selectChannel: 'Select Channel',
    channelSimulation: 'Channel Simulation',
    simulateHow: 'Simulate how your bot responds on different platforms',
  },
  es: {
    // Navigation
    navigation: 'Navegación',
    dashboard: 'Panel de Control',
    botTesting: 'Prueba de Bot',
    messages: 'Mensajes',
    analytics: 'Analíticas',
    settings: 'Configuración',
    pricing: 'Precios',
    profile: 'Perfil',
    notifications: 'Notificaciones',
    security: 'Seguridad',
    billing: 'Facturación',
    apiKeys: 'Claves API',
    signOut: 'Cerrar Sesión',
    language: 'Idioma',
    selectLanguage: 'Seleccionar Idioma',

    // Common actions
    save: 'Guardar',
    cancel: 'Cancelar',
    loading: 'Cargando...',
    error: 'Error',
    success: 'Éxito',
    edit: 'Editar',
    delete: 'Eliminar',
    create: 'Crear',
    update: 'Actualizar',
    confirm: 'Confirmar',
    close: 'Cerrar',

    // Landing page
    heroTitle1: 'Chatea,',
    heroTitle2: 'Crea,',
    heroTitle3: 'Despliega',
    heroSubtitle: 'Construye chatbots inteligentes para WhatsApp, SMS y Messenger en minutos. No se requiere programación.',
    getStarted: 'Comenzar Gratis',
    signIn: 'Iniciar Sesión',
    trustedBy: 'Confiado por más de 500 pequeñas empresas en todo el mundo',
    instantSetup: 'Configuración Instantánea',
    instantSetupDesc: 'Ten tu chatbot listo en minutos, no horas',
    multiPlatform: 'Multi-Plataforma',
    multiPlatformDesc: 'Funciona en WhatsApp, Facebook y SMS',
    securePrivate: 'Seguro y Privado',
    securePrivateDesc: 'Tus datos permanecen protegidos y confidenciales',

    // Dashboard
    myBots: 'Mis Bots',
    createBot: 'Crear Nuevo Bot',
    noBots: 'Aún no se han creado bots',
    createFirstBot: 'Crea tu primer chatbot para comenzar',
    recentMessages: 'Mensajes Recientes',
    noMessages: 'Aún no hay mensajes',
    startConversation: 'Inicia una conversación con tus bots para ver mensajes aquí',
    manageBots: 'Gestiona y monitorea tus chatbots activos',
    totalMessages: 'Mensajes Totales',
    activeUsers: 'Usuarios Activos',
    activeBots: 'Bots Activos',
    tokensUsed: 'Tokens Utilizados',
    fromLastMonth: 'del mes pasado',
    created: 'Creado',
    editBot: 'Editar Bot',
    viewAnalytics: 'Ver Analíticas',
    testBot: 'Probar Bot',
    deleteBot: 'Eliminar Bot',

    // Settings
    profileInformation: 'Información del Perfil',
    updatePersonalInfo: 'Actualiza tu información personal y detalles comerciales',
    firstName: 'Nombre',
    lastName: 'Apellido',
    emailAddress: 'Dirección de Email',
    businessName: 'Nombre del Negocio',
    timezone: 'Zona Horaria',
    emailCannotChange: 'El email no se puede cambiar aquí. Contacta soporte si es necesario.',
    notificationPreferences: 'Preferencias de Notificación',
    chooseNotifications: 'Elige qué notificaciones quieres recibir',
    newMessages: 'Nuevos Mensajes',
    newMessagesDesc: 'Recibe notificaciones cuando los usuarios envíen mensajes a tus bots',
    botPerformanceAlerts: 'Alertas de Rendimiento del Bot',
    botPerformanceDesc: 'Recibe alertas sobre problemas de rendimiento del bot',
    usageWarnings: 'Advertencias de Uso',
    usageWarningsDesc: 'Recibe notificaciones al acercarte a los límites de tokens',
    weeklyReports: 'Reportes Semanales',
    weeklyReportsDesc: 'Recibe resúmenes semanales de rendimiento',
    saveNotificationSettings: 'Guardar Configuración de Notificaciones',

    // Notifications/Toasts
    languageChanged: 'Idioma cambiado a',
    failedToChangeLanguage: 'Error al cambiar idioma. Inténtalo de nuevo.',
    profileUpdated: 'Perfil Actualizado',
    profileUpdateSuccess: 'Tu información de perfil se ha actualizado exitosamente.',
    updateFailed: 'Actualización Fallida',
    updateFailedDesc: 'Error al actualizar perfil. Inténtalo de nuevo.',
    notificationPrefsUpdated: 'Preferencias de Notificación Actualizadas',
    notificationPrefsSaved: 'Tu configuración de notificaciones ha sido guardada.',

    // Help & Support
    helpSupport: 'Ayuda y Soporte',
    contactSupport: 'Contá<NAME_EMAIL> para asistencia',

    // Analytics page
    trackPerformance: 'Rastrea el rendimiento y perspectivas de tu chatbot',
    exportReport: 'Exportar Reporte',
    last7Days: 'Últimos 7 días',
    last30Days: 'Últimos 30 días',
    last90Days: 'Últimos 90 días',
    lastYear: 'Último año',
    totalConversations: 'Conversaciones Totales',
    activeUsers: 'Usuarios Activos',
    avgResponseTime: 'Tiempo de Respuesta Promedio',
    resolutionRate: 'Tasa de Resolución',
    vsLastPeriod: 'vs período anterior',
    messagesUsersOverTime: 'Mensajes y Usuarios en el Tiempo',
    monthlyTrends: 'Tendencias mensuales de conversaciones y usuarios',
    channelDistribution: 'Distribución de Canales',
    messagesByChannel: 'Mensajes por canal de comunicación',
    botPerformance: 'Rendimiento del Bot',
    individualBotStats: 'Estadísticas y métricas individuales del chatbot',
    totalMessages: 'mensajes totales',
    satisfaction: 'Satisfacción',
    avgResponse: 'Respuesta Promedio',
    active: 'Activo',

    // Messages page
    viewManageConversations: 'Ver y gestionar conversaciones del bot',
    searchConversations: 'Buscar conversaciones...',
    allBots: 'Todos los Bots',
    allChannels: 'Todos los Canales',
    recentConversations: 'Conversaciones Recientes',
    noConversationsYet: 'Aún no hay conversaciones',
    messagesWillAppear: '¡Los mensajes aparecerán aquí una vez que tus bots empiecen a chatear!',
    selectConversation: 'Seleccionar una conversación',
    noConversationSelected: 'Ninguna conversación seleccionada',
    selectConversationLeft: 'Selecciona una conversación de la izquierda para ver mensajes',
    markAsResolved: 'Marcar como Resuelto',
    transferToHuman: 'Transferir a Humano',
    exportChat: 'Exportar Chat',

    // Settings page
    manageAccountPreferences: 'Gestiona tu cuenta y preferencias de chatbot',
    settingsMenu: 'Menú de Configuración',
    profileInformation: 'Información del Perfil',
    updatePersonalInfo: 'Actualiza tu información personal y detalles comerciales',
    firstName: 'Nombre',
    lastName: 'Apellido',
    emailAddress: 'Dirección de Email',
    businessName: 'Nombre del Negocio',
    timezone: 'Zona Horaria',
    emailCannotChange: 'El email no se puede cambiar aquí. Contacta soporte si es necesario.',
    enterFirstName: 'Ingresa tu nombre',
    enterLastName: 'Ingresa tu apellido',
    enterBusinessName: 'Ingresa el nombre de tu negocio',
    selectTimezone: 'Seleccionar zona horaria',
    easternTime: 'Hora del Este (ET)',
    centralTime: 'Hora Central (CT)',
    mountainTime: 'Hora de la Montaña (MT)',
    pacificTime: 'Hora del Pacífico (PT)',
    saving: 'Guardando...',
    saveChanges: 'Guardar Cambios',

    // Notification settings
    notificationPreferences: 'Preferencias de Notificación',
    chooseNotifications: 'Elige qué notificaciones quieres recibir',
    newMessages: 'Nuevos Mensajes',
    newMessagesDesc: 'Recibe notificaciones cuando los usuarios envíen mensajes a tus bots',
    botPerformanceAlerts: 'Alertas de Rendimiento del Bot',
    botPerformanceDesc: 'Recibe alertas sobre problemas de rendimiento del bot',
    usageWarnings: 'Advertencias de Uso',
    usageWarningsDesc: 'Recibe notificaciones al acercarte a los límites de tokens',
    weeklyReports: 'Reportes Semanales',
    weeklyReportsDesc: 'Recibe resúmenes semanales de rendimiento',
    saveNotificationSettings: 'Guardar Configuración de Notificaciones',
    notificationPrefsUpdated: 'Preferencias de Notificación Actualizadas',
    notificationPrefsSaved: 'Tu configuración de notificaciones ha sido guardada.',

    // API Keys
    apiKeys: 'Claves API',
    manageApiKeys: 'Gestiona tus claves API para integraciones',
    whatsappBusinessApi: 'API de WhatsApp Business',
    facebookMessenger: 'Facebook Messenger',
    smsGateway: 'Puerta de Enlace SMS',
    notConnected: 'No conectado',
    connect: 'Conectar',
    comingSoon: 'Próximamente',
    facebookIntegrationSoon: 'La integración de Facebook Messenger estará disponible pronto.',
    smsIntegrationSoon: 'La integración de Puerta de Enlace SMS estará disponible pronto.',
    generateNewApiKey: 'Generar Nueva Clave API',
    apiKeyGenerated: 'Clave API Generada',
    apiKeyGeneratedDesc: 'Se ha generado una nueva clave API y se ha enviado a tu email.',

    // Billing
    billingSubscription: 'Facturación y Suscripción',
    manageBillingInfo: 'Gestiona tu suscripción e información de facturación',
    currentPlan: 'Plan Actual',
    currentPlanFree: 'Actualmente estás en el plan gratuito con 500 mensajes por mes.',
    upgradePlan: 'Actualizar Plan',
    redirectingToPricing: 'Redirigiendo a Precios',
    takingToPricing: 'Te llevamos a nuestra página de precios para actualizar tu plan.',
    usageThisMonth: 'Uso Este Mes',
    messagesUsed: 'Mensajes Utilizados',

    // Security
    securitySettings: 'Configuración de Seguridad',
    manageAccountSecurity: 'Gestiona la seguridad y privacidad de tu cuenta',
    passwordSecurity: 'Seguridad de Contraseña',
    keepAccountSecure: 'Mantén tu cuenta segura actualizando tu contraseña',
    changePassword: 'Cambiar Contraseña',
    updateYourPassword: 'Actualizar Tu Contraseña',
    currentPassword: 'Contraseña Actual',
    newPassword: 'Nueva Contraseña',
    confirmNewPassword: 'Confirmar Nueva Contraseña',
    enterCurrentPassword: 'Ingresa tu contraseña actual',
    enterNewPassword: 'Ingresa tu nueva contraseña',
    confirmNewPasswordPlaceholder: 'Confirma tu nueva contraseña',
    passwordMinLength: 'La contraseña debe tener al menos 6 caracteres',
    updatingPassword: 'Actualizando Contraseña...',
    updatePassword: 'Actualizar Contraseña',
    dangerZone: 'Zona de Peligro',
    disableAccount: 'Deshabilitar Cuenta',
    disableAccountDesc: 'Deshabilita permanentemente tu cuenta y previene acceso futuro. Esta acción no se puede deshacer.',
    areYouSure: '¿Estás absolutamente seguro?',
    disableAccountWarning: 'Esto deshabilitará permanentemente tu cuenta. No podrás iniciar sesión nuevamente.',
    disabling: 'Deshabilitando...',
    yesDisableAccount: 'Sí, Deshabilitar Mi Cuenta',

    // Validation messages
    validationError: 'Error de Validación',
    passwordTooShort: 'La nueva contraseña debe tener al menos 6 caracteres',
    passwordsDoNotMatch: 'Las nuevas contraseñas no coinciden',
    currentPasswordRequired: 'Se requiere la contraseña actual',
    passwordUpdated: 'Contraseña Actualizada',
    passwordUpdateSuccess: 'Tu contraseña se ha actualizado exitosamente.',
    passwordUpdateFailed: 'Falló la Actualización de Contraseña',
    currentPasswordIncorrect: 'La contraseña actual es incorrecta',
    accountDisabled: 'Cuenta Deshabilitada',
    accountDisabledDesc: 'Tu cuenta ha sido deshabilitada. Ahora serás desconectado.',
    accountDeletionFailed: 'Falló la Eliminación de Cuenta',
    failedToDisableAccount: 'Falló deshabilitar la cuenta. Inténtalo de nuevo.',

    // ChatInterface
    chatbotGreeting: '¡Hola! 👋 Cuéntame sobre tu negocio: ¿qué haces, qué servicios ofreces y qué debería saber tu chatbot?',
    typeMessage: 'Escribe tu mensaje...',
    attachFile: 'Adjuntar archivo',
    sendMessage: 'Enviar mensaje',
    uploadFiles: 'Subir Archivos',
    dragDropFiles: 'Arrastra y suelta archivos aquí, o haz clic para seleccionar',
    supportedFormats: 'Formatos soportados: PDF, TXT, DOC, DOCX (máx. 10MB cada uno)',
    filesUploaded: 'archivos subidos',
    removeFile: 'Eliminar archivo',
    createChatbot: 'Crear Chatbot',
    creatingBot: 'Creando tu bot...',
    chatbotCreated: '¡Chatbot Creado!',
    chatbotCreatedDesc: 'Tu chatbot ha sido creado exitosamente.',
    chatbotCreationFailed: 'Falló la Creación del Chatbot',
    failedToCreateBot: 'Falló crear el chatbot. Inténtalo de nuevo.',
    fileUploadFailed: 'Falló la Subida del Archivo',
    fileTooLarge: 'El archivo es muy grande. El tamaño máximo es 10MB.',
    unsupportedFileType: 'Tipo de archivo no soportado. Usa PDF, TXT, DOC, o DOCX.',
    uploadError: 'Error subiendo archivo. Inténtalo de nuevo.',
    thinkingResponse: 'Pensando...',

    // BotTesting
    testYourChatbot: 'Prueba Tu Chatbot',
    simulateConversations: 'Simula conversaciones en diferentes canales',
    botGreeting: '¡Hola! Soy tu Asistente de Restaurante. ¿Cómo puedo ayudarte hoy?',
    testConversation: 'Conversación de Prueba',
    selectChannel: 'Seleccionar Canal',
    channelSimulation: 'Simulación de Canal',
    simulateHow: 'Simula cómo responde tu bot en diferentes plataformas',
  },
  fr: {
    dashboard: 'Tableau de Bord',
    botTesting: 'Test de Bot',
    messages: 'Messages',
    analytics: 'Analyses',
    settings: 'Paramètres',
    pricing: 'Tarifs',
    profile: 'Profil',
    notifications: 'Notifications',
    security: 'Sécurité',
    billing: 'Facturation',
    apiKeys: 'Clés API',
    signOut: 'Se Déconnecter',
    language: 'Langue',
    selectLanguage: 'Sélectionner la Langue',
    save: 'Enregistrer',
    cancel: 'Annuler',
    loading: 'Chargement...',
    error: 'Erreur',
    success: 'Succès',
    // Landing page
    heroTitle1: 'Chattez,',
    heroTitle2: 'Créez,',
    heroTitle3: 'Déployez',
    heroSubtitle: 'Construisez des chatbots intelligents pour WhatsApp, SMS et Messenger en quelques minutes. Aucun codage requis.',
    getStarted: 'Commencer Gratuitement',
    signIn: 'Se Connecter',
    // Dashboard
    myBots: 'Mes Bots',
    createBot: 'Créer un Nouveau Bot',
    noBots: 'Aucun bot créé pour le moment',
    createFirstBot: 'Créez votre premier chatbot pour commencer',
    recentMessages: 'Messages Récents',
    noMessages: 'Aucun message pour le moment',
    startConversation: 'Commencez une conversation avec vos bots pour voir les messages ici',
  },
  de: {
    dashboard: 'Dashboard',
    botTesting: 'Bot-Test',
    messages: 'Nachrichten',
    analytics: 'Analytik',
    settings: 'Einstellungen',
    pricing: 'Preise',
    profile: 'Profil',
    notifications: 'Benachrichtigungen',
    security: 'Sicherheit',
    billing: 'Abrechnung',
    apiKeys: 'API-Schlüssel',
    signOut: 'Abmelden',
    language: 'Sprache',
    selectLanguage: 'Sprache Auswählen',
    save: 'Speichern',
    cancel: 'Abbrechen',
    loading: 'Laden...',
    error: 'Fehler',
    success: 'Erfolg',
    // Landing page
    heroTitle1: 'Chatten,',
    heroTitle2: 'Erstellen,',
    heroTitle3: 'Bereitstellen',
    heroSubtitle: 'Erstellen Sie intelligente Chatbots für WhatsApp, SMS und Messenger in Minuten. Keine Programmierung erforderlich.',
    getStarted: 'Kostenlos Starten',
    signIn: 'Anmelden',
    // Dashboard
    myBots: 'Meine Bots',
    createBot: 'Neuen Bot Erstellen',
    noBots: 'Noch keine Bots erstellt',
    createFirstBot: 'Erstellen Sie Ihren ersten Chatbot, um zu beginnen',
    recentMessages: 'Aktuelle Nachrichten',
    noMessages: 'Noch keine Nachrichten',
    startConversation: 'Starten Sie eine Unterhaltung mit Ihren Bots, um hier Nachrichten zu sehen',
  },
  it: {
    dashboard: 'Dashboard',
    botTesting: 'Test Bot',
    messages: 'Messaggi',
    analytics: 'Analisi',
    settings: 'Impostazioni',
    pricing: 'Prezzi',
    profile: 'Profilo',
    notifications: 'Notifiche',
    security: 'Sicurezza',
    billing: 'Fatturazione',
    apiKeys: 'Chiavi API',
    signOut: 'Disconnetti',
    language: 'Lingua',
    selectLanguage: 'Seleziona Lingua',
    save: 'Salva',
    cancel: 'Annulla',
    loading: 'Caricamento...',
    error: 'Errore',
    success: 'Successo',
    // Landing page
    heroTitle1: 'Chatta,',
    heroTitle2: 'Crea,',
    heroTitle3: 'Distribuisci',
    heroSubtitle: 'Costruisci chatbot intelligenti per WhatsApp, SMS e Messenger in pochi minuti. Nessuna programmazione richiesta.',
    getStarted: 'Inizia Gratis',
    signIn: 'Accedi',
    // Dashboard
    myBots: 'I Miei Bot',
    createBot: 'Crea Nuovo Bot',
    noBots: 'Nessun bot creato ancora',
    createFirstBot: 'Crea il tuo primo chatbot per iniziare',
    recentMessages: 'Messaggi Recenti',
    noMessages: 'Nessun messaggio ancora',
    startConversation: 'Inizia una conversazione con i tuoi bot per vedere i messaggi qui',
  },
  pt: {
    dashboard: 'Painel',
    botTesting: 'Teste de Bot',
    messages: 'Mensagens',
    analytics: 'Análises',
    settings: 'Configurações',
    pricing: 'Preços',
    profile: 'Perfil',
    notifications: 'Notificações',
    security: 'Segurança',
    billing: 'Faturamento',
    apiKeys: 'Chaves API',
    signOut: 'Sair',
    language: 'Idioma',
    selectLanguage: 'Selecionar Idioma',
    save: 'Salvar',
    cancel: 'Cancelar',
    loading: 'Carregando...',
    error: 'Erro',
    success: 'Sucesso',
    // Landing page
    heroTitle1: 'Converse,',
    heroTitle2: 'Crie,',
    heroTitle3: 'Implante',
    heroSubtitle: 'Construa chatbots inteligentes para WhatsApp, SMS e Messenger em minutos. Nenhuma programação necessária.',
    getStarted: 'Começar Grátis',
    signIn: 'Entrar',
    // Dashboard
    myBots: 'Meus Bots',
    createBot: 'Criar Novo Bot',
    noBots: 'Nenhum bot criado ainda',
    createFirstBot: 'Crie seu primeiro chatbot para começar',
    recentMessages: 'Mensagens Recentes',
    noMessages: 'Nenhuma mensagem ainda',
    startConversation: 'Inicie uma conversa com seus bots para ver mensagens aqui',
  },
  zh: {
    dashboard: '仪表板',
    botTesting: '机器人测试',
    messages: '消息',
    analytics: '分析',
    settings: '设置',
    pricing: '定价',
    profile: '个人资料',
    notifications: '通知',
    security: '安全',
    billing: '账单',
    apiKeys: 'API密钥',
    signOut: '退出',
    language: '语言',
    selectLanguage: '选择语言',
    save: '保存',
    cancel: '取消',
    loading: '加载中...',
    error: '错误',
    success: '成功',
    // Landing page
    heroTitle1: '聊天，',
    heroTitle2: '创建，',
    heroTitle3: '部署',
    heroSubtitle: '在几分钟内为WhatsApp、短信和Messenger构建智能聊天机器人。无需编程。',
    getStarted: '免费开始',
    signIn: '登录',
    // Dashboard
    myBots: '我的机器人',
    createBot: '创建新机器人',
    noBots: '还没有创建机器人',
    createFirstBot: '创建您的第一个聊天机器人开始使用',
    recentMessages: '最近消息',
    noMessages: '还没有消息',
    startConversation: '与您的机器人开始对话以在此处查看消息',
  },
  ja: {
    dashboard: 'ダッシュボード',
    botTesting: 'ボットテスト',
    messages: 'メッセージ',
    analytics: '分析',
    settings: '設定',
    pricing: '料金',
    profile: 'プロフィール',
    notifications: '通知',
    security: 'セキュリティ',
    billing: '請求',
    apiKeys: 'APIキー',
    signOut: 'サインアウト',
    language: '言語',
    selectLanguage: '言語を選択',
    save: '保存',
    cancel: 'キャンセル',
    loading: '読み込み中...',
    error: 'エラー',
    success: '成功',
    // Landing page
    heroTitle1: 'チャット、',
    heroTitle2: '作成、',
    heroTitle3: 'デプロイ',
    heroSubtitle: 'WhatsApp、SMS、Messengerのための知能的なチャットボットを数分で構築。プログラミング不要。',
    getStarted: '無料で始める',
    signIn: 'サインイン',
    // Dashboard
    myBots: 'マイボット',
    createBot: '新しいボットを作成',
    noBots: 'まだボットが作成されていません',
    createFirstBot: '最初のチャットボットを作成して始めましょう',
    recentMessages: '最近のメッセージ',
    noMessages: 'まだメッセージがありません',
    startConversation: 'ボットとの会話を開始してここでメッセージを確認してください',
  },
  ko: {
    dashboard: '대시보드',
    botTesting: '봇 테스트',
    messages: '메시지',
    analytics: '분석',
    settings: '설정',
    pricing: '가격',
    profile: '프로필',
    notifications: '알림',
    security: '보안',
    billing: '청구',
    apiKeys: 'API 키',
    signOut: '로그아웃',
    language: '언어',
    selectLanguage: '언어 선택',
    save: '저장',
    cancel: '취소',
    loading: '로딩 중...',
    error: '오류',
    success: '성공',
    // Landing page
    heroTitle1: '채팅,',
    heroTitle2: '생성,',
    heroTitle3: '배포',
    heroSubtitle: 'WhatsApp, SMS, Messenger를 위한 지능형 챗봇을 몇 분 만에 구축하세요. 코딩이 필요하지 않습니다.',
    getStarted: '무료로 시작하기',
    signIn: '로그인',
    // Dashboard
    myBots: '내 봇',
    createBot: '새 봇 만들기',
    noBots: '아직 생성된 봇이 없습니다',
    createFirstBot: '첫 번째 챗봇을 만들어 시작하세요',
    recentMessages: '최근 메시지',
    noMessages: '아직 메시지가 없습니다',
    startConversation: '봇과 대화를 시작하여 여기에서 메시지를 확인하세요',
  },
  ar: {
    // Navigation
    navigation: 'التنقل',
    dashboard: 'لوحة التحكم',
    botTesting: 'اختبار البوت',
    messages: 'الرسائل',
    analytics: 'التحليلات',
    settings: 'الإعدادات',
    pricing: 'التسعير',
    profile: 'الملف الشخصي',
    notifications: 'الإشعارات',
    security: 'الأمان',
    billing: 'الفواتير',
    apiKeys: 'مفاتيح API',
    signOut: 'تسجيل الخروج',
    language: 'اللغة',
    selectLanguage: 'اختر اللغة',

    // Common actions
    save: 'حفظ',
    cancel: 'إلغاء',
    loading: 'جاري التحميل...',
    error: 'خطأ',
    success: 'نجح',
    edit: 'تعديل',
    delete: 'حذف',
    create: 'إنشاء',
    update: 'تحديث',
    confirm: 'تأكيد',
    close: 'إغلاق',

    // Landing page
    heroTitle1: 'دردش،',
    heroTitle2: 'أنشئ،',
    heroTitle3: 'انشر',
    heroSubtitle: 'قم ببناء روبوتات محادثة ذكية لـ WhatsApp و SMS و Messenger في دقائق. لا حاجة للبرمجة.',
    getStarted: 'ابدأ مجاناً',
    signIn: 'تسجيل الدخول',
    trustedBy: 'موثوق به من قبل أكثر من 500 شركة صغيرة حول العالم',
    instantSetup: 'إعداد فوري',
    instantSetupDesc: 'اجعل روبوت المحادثة جاهزاً في دقائق، وليس ساعات',
    multiPlatform: 'متعدد المنصات',
    multiPlatformDesc: 'يعمل على WhatsApp و Facebook و SMS',
    securePrivate: 'آمن وخاص',
    securePrivateDesc: 'بياناتك تبقى محمية وسرية',

    // Dashboard
    myBots: 'روبوتاتي',
    createBot: 'إنشاء روبوت جديد',
    noBots: 'لم يتم إنشاء أي روبوتات بعد',
    createFirstBot: 'أنشئ أول روبوت محادثة للبدء',
    recentMessages: 'الرسائل الأخيرة',
    noMessages: 'لا توجد رسائل بعد',
    startConversation: 'ابدأ محادثة مع روبوتاتك لرؤية الرسائل هنا',
    manageBots: 'إدارة ومراقبة روبوتات المحادثة النشطة',
    totalMessages: 'إجمالي الرسائل',
    activeUsers: 'المستخدمون النشطون',
    activeBots: 'الروبوتات النشطة',
    tokensUsed: 'الرموز المستخدمة',
    fromLastMonth: 'من الشهر الماضي',
    created: 'تم الإنشاء',
    editBot: 'تعديل الروبوت',
    viewAnalytics: 'عرض التحليلات',
    testBot: 'اختبار الروبوت',
    deleteBot: 'حذف الروبوت',

    // Settings
    profileInformation: 'معلومات الملف الشخصي',
    updatePersonalInfo: 'قم بتحديث معلوماتك الشخصية وتفاصيل العمل',
    firstName: 'الاسم الأول',
    lastName: 'اسم العائلة',
    emailAddress: 'عنوان البريد الإلكتروني',
    businessName: 'اسم العمل',
    timezone: 'المنطقة الزمنية',
    emailCannotChange: 'لا يمكن تغيير البريد الإلكتروني هنا. اتصل بالدعم إذا لزم الأمر.',
    notificationPreferences: 'تفضيلات الإشعارات',
    chooseNotifications: 'اختر الإشعارات التي تريد تلقيها',
    newMessages: 'رسائل جديدة',
    newMessagesDesc: 'احصل على إشعارات عندما يرسل المستخدمون رسائل لروبوتاتك',
    botPerformanceAlerts: 'تنبيهات أداء الروبوت',
    botPerformanceDesc: 'تلقى تنبيهات حول مشاكل أداء الروبوت',
    usageWarnings: 'تحذيرات الاستخدام',
    usageWarningsDesc: 'احصل على إشعارات عند الاقتراب من حدود الرموز',
    weeklyReports: 'التقارير الأسبوعية',
    weeklyReportsDesc: 'تلقى ملخصات أداء أسبوعية',
    saveNotificationSettings: 'حفظ إعدادات الإشعارات',

    // Notifications/Toasts
    languageChanged: 'تم تغيير اللغة إلى',
    failedToChangeLanguage: 'فشل في تغيير اللغة. حاول مرة أخرى.',
    profileUpdated: 'تم تحديث الملف الشخصي',
    profileUpdateSuccess: 'تم تحديث معلومات ملفك الشخصي بنجاح.',
    updateFailed: 'فشل التحديث',
    updateFailedDesc: 'فشل في تحديث الملف الشخصي. حاول مرة أخرى.',
    notificationPrefsUpdated: 'تم تحديث تفضيلات الإشعارات',
    notificationPrefsSaved: 'تم حفظ إعدادات الإشعارات الخاصة بك.',

    // Help & Support
    helpSupport: 'المساعدة والدعم',
    contactSupport: 'اتصل بنا على <EMAIL> للمساعدة',

    // Analytics page
    trackPerformance: 'تتبع أداء وإحصائيات روبوت المحادثة الخاص بك',
    exportReport: 'تصدير التقرير',
    last7Days: 'آخر 7 أيام',
    last30Days: 'آخر 30 يوماً',
    last90Days: 'آخر 90 يوماً',
    lastYear: 'العام الماضي',
    totalConversations: 'إجمالي المحادثات',
    activeUsers: 'المستخدمون النشطون',
    avgResponseTime: 'متوسط وقت الاستجابة',
    resolutionRate: 'معدل الحل',
    vsLastPeriod: 'مقارنة بالفترة السابقة',
    messagesUsersOverTime: 'الرسائل والمستخدمون عبر الزمن',
    monthlyTrends: 'اتجاهات المحادثات والمستخدمين الشهرية',
    channelDistribution: 'توزيع القنوات',
    messagesByChannel: 'الرسائل حسب قناة التواصل',
    botPerformance: 'أداء الروبوت',
    individualBotStats: 'إحصائيات ومقاييس الروبوت الفردية',
    totalMessages: 'إجمالي الرسائل',
    satisfaction: 'الرضا',
    avgResponse: 'متوسط الاستجابة',
    active: 'نشط',

    // Messages page
    viewManageConversations: 'عرض وإدارة محادثات الروبوت',
    searchConversations: 'البحث في المحادثات...',
    allBots: 'جميع الروبوتات',
    allChannels: 'جميع القنوات',
    recentConversations: 'المحادثات الأخيرة',
    noConversationsYet: 'لا توجد محادثات بعد',
    messagesWillAppear: 'ستظهر الرسائل هنا بمجرد أن تبدأ روبوتاتك في المحادثة!',
    selectConversation: 'اختر محادثة',
    noConversationSelected: 'لم يتم اختيار محادثة',
    selectConversationLeft: 'اختر محادثة من اليسار لعرض الرسائل',
    markAsResolved: 'وضع علامة كمحلول',
    transferToHuman: 'نقل إلى إنسان',
    exportChat: 'تصدير المحادثة',

    // Settings page
    manageAccountPreferences: 'إدارة حسابك وتفضيلات روبوت المحادثة',
    settingsMenu: 'قائمة الإعدادات',
    profileInformation: 'معلومات الملف الشخصي',
    updatePersonalInfo: 'قم بتحديث معلوماتك الشخصية وتفاصيل العمل',
    firstName: 'الاسم الأول',
    lastName: 'اسم العائلة',
    emailAddress: 'عنوان البريد الإلكتروني',
    businessName: 'اسم العمل',
    timezone: 'المنطقة الزمنية',
    emailCannotChange: 'لا يمكن تغيير البريد الإلكتروني هنا. اتصل بالدعم إذا لزم الأمر.',
    enterFirstName: 'أدخل اسمك الأول',
    enterLastName: 'أدخل اسم عائلتك',
    enterBusinessName: 'أدخل اسم عملك',
    selectTimezone: 'اختر المنطقة الزمنية',
    easternTime: 'التوقيت الشرقي (ET)',
    centralTime: 'التوقيت المركزي (CT)',
    mountainTime: 'توقيت الجبل (MT)',
    pacificTime: 'توقيت المحيط الهادئ (PT)',
    saving: 'جاري الحفظ...',
    saveChanges: 'حفظ التغييرات',

    // Notification settings
    notificationPreferences: 'تفضيلات الإشعارات',
    chooseNotifications: 'اختر الإشعارات التي تريد تلقيها',
    newMessages: 'رسائل جديدة',
    newMessagesDesc: 'احصل على إشعارات عندما يرسل المستخدمون رسائل لروبوتاتك',
    botPerformanceAlerts: 'تنبيهات أداء الروبوت',
    botPerformanceDesc: 'تلقى تنبيهات حول مشاكل أداء الروبوت',
    usageWarnings: 'تحذيرات الاستخدام',
    usageWarningsDesc: 'احصل على إشعارات عند الاقتراب من حدود الرموز',
    weeklyReports: 'التقارير الأسبوعية',
    weeklyReportsDesc: 'تلقى ملخصات أداء أسبوعية',
    saveNotificationSettings: 'حفظ إعدادات الإشعارات',
    notificationPrefsUpdated: 'تم تحديث تفضيلات الإشعارات',
    notificationPrefsSaved: 'تم حفظ إعدادات الإشعارات الخاصة بك.',

    // API Keys
    apiKeys: 'مفاتيح API',
    manageApiKeys: 'إدارة مفاتيح API للتكاملات',
    whatsappBusinessApi: 'واجهة برمجة تطبيقات WhatsApp Business',
    facebookMessenger: 'Facebook Messenger',
    smsGateway: 'بوابة الرسائل النصية',
    notConnected: 'غير متصل',
    connect: 'اتصال',
    comingSoon: 'قريباً',
    facebookIntegrationSoon: 'تكامل Facebook Messenger سيكون متاحاً قريباً.',
    smsIntegrationSoon: 'تكامل بوابة الرسائل النصية سيكون متاحاً قريباً.',
    generateNewApiKey: 'إنشاء مفتاح API جديد',
    apiKeyGenerated: 'تم إنشاء مفتاح API',
    apiKeyGeneratedDesc: 'تم إنشاء مفتاح API جديد وإرساله إلى بريدك الإلكتروني.',

    // Billing
    billingSubscription: 'الفواتير والاشتراك',
    manageBillingInfo: 'إدارة اشتراكك ومعلومات الفواتير',
    currentPlan: 'الخطة الحالية',
    currentPlanFree: 'أنت حالياً على الخطة المجانية مع 500 رسالة شهرياً.',
    upgradePlan: 'ترقية الخطة',
    redirectingToPricing: 'إعادة توجيه إلى التسعير',
    takingToPricing: 'نأخذك إلى صفحة التسعير لترقية خطتك.',
    usageThisMonth: 'الاستخدام هذا الشهر',
    messagesUsed: 'الرسائل المستخدمة',

    // Security
    securitySettings: 'إعدادات الأمان',
    manageAccountSecurity: 'إدارة أمان وخصوصية حسابك',
    passwordSecurity: 'أمان كلمة المرور',
    keepAccountSecure: 'حافظ على أمان حسابك بتحديث كلمة المرور',
    changePassword: 'تغيير كلمة المرور',
    updateYourPassword: 'تحديث كلمة المرور',
    currentPassword: 'كلمة المرور الحالية',
    newPassword: 'كلمة المرور الجديدة',
    confirmNewPassword: 'تأكيد كلمة المرور الجديدة',
    enterCurrentPassword: 'أدخل كلمة المرور الحالية',
    enterNewPassword: 'أدخل كلمة المرور الجديدة',
    confirmNewPasswordPlaceholder: 'أكد كلمة المرور الجديدة',
    passwordMinLength: 'يجب أن تكون كلمة المرور 6 أحرف على الأقل',
    updatingPassword: 'جاري تحديث كلمة المرور...',
    updatePassword: 'تحديث كلمة المرور',
    dangerZone: 'منطقة الخطر',
    disableAccount: 'تعطيل الحساب',
    disableAccountDesc: 'تعطيل حسابك نهائياً ومنع الوصول المستقبلي. هذا الإجراء لا يمكن التراجع عنه.',
    areYouSure: 'هل أنت متأكد تماماً؟',
    disableAccountWarning: 'سيؤدي هذا إلى تعطيل حسابك نهائياً. لن تتمكن من تسجيل الدخول مرة أخرى.',
    disabling: 'جاري التعطيل...',
    yesDisableAccount: 'نعم، عطل حسابي',

    // Validation messages
    validationError: 'خطأ في التحقق',
    passwordTooShort: 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل',
    passwordsDoNotMatch: 'كلمات المرور الجديدة غير متطابقة',
    currentPasswordRequired: 'كلمة المرور الحالية مطلوبة',
    passwordUpdated: 'تم تحديث كلمة المرور',
    passwordUpdateSuccess: 'تم تحديث كلمة المرور بنجاح.',
    passwordUpdateFailed: 'فشل تحديث كلمة المرور',
    currentPasswordIncorrect: 'كلمة المرور الحالية غير صحيحة',
    accountDisabled: 'تم تعطيل الحساب',
    accountDisabledDesc: 'تم تعطيل حسابك. سيتم تسجيل خروجك الآن.',
    accountDeletionFailed: 'فشل حذف الحساب',
    failedToDisableAccount: 'فشل في تعطيل الحساب. حاول مرة أخرى.',

    // ChatInterface
    chatbotGreeting: 'مرحباً! 👋 أخبرني عن عملك - ماذا تفعل، ما الخدمات التي تقدمها، وماذا يجب أن يعرف روبوت المحادثة الخاص بك؟',
    typeMessage: 'اكتب رسالتك...',
    attachFile: 'إرفاق ملف',
    sendMessage: 'إرسال الرسالة',
    uploadFiles: 'رفع الملفات',
    dragDropFiles: 'اسحب وأفلت الملفات هنا، أو انقر للاختيار',
    supportedFormats: 'الصيغ المدعومة: PDF, TXT, DOC, DOCX (حد أقصى 10 ميجابايت لكل ملف)',
    filesUploaded: 'ملفات مرفوعة',
    removeFile: 'إزالة الملف',
    createChatbot: 'إنشاء روبوت المحادثة',
    creatingBot: 'جاري إنشاء الروبوت...',
    chatbotCreated: 'تم إنشاء روبوت المحادثة!',
    chatbotCreatedDesc: 'تم إنشاء روبوت المحادثة بنجاح.',
    chatbotCreationFailed: 'فشل إنشاء روبوت المحادثة',
    failedToCreateBot: 'فشل في إنشاء روبوت المحادثة. حاول مرة أخرى.',
    fileUploadFailed: 'فشل رفع الملف',
    fileTooLarge: 'الملف كبير جداً. الحد الأقصى هو 10 ميجابايت.',
    unsupportedFileType: 'نوع ملف غير مدعوم. استخدم PDF, TXT, DOC, أو DOCX.',
    uploadError: 'خطأ في رفع الملف. حاول مرة أخرى.',
    thinkingResponse: 'جاري التفكير...',

    // BotTesting
    testYourChatbot: 'اختبر روبوت المحادثة',
    simulateConversations: 'محاكاة المحادثات عبر قنوات مختلفة',
    botGreeting: 'مرحباً! أنا مساعد المطعم الخاص بك. كيف يمكنني مساعدتك اليوم؟',
    testConversation: 'محادثة تجريبية',
    selectChannel: 'اختر القناة',
    channelSimulation: 'محاكاة القناة',
    simulateHow: 'محاكاة كيفية استجابة الروبوت على منصات مختلفة',
  },
};

interface LanguageContextType {
  currentLanguage: LanguageCode;
  setLanguage: (language: LanguageCode) => Promise<void>;
  resetLanguage: () => void;
  t: (key: keyof typeof translations.en) => string;
  loading: boolean;
  isRTL: boolean;
  direction: 'ltr' | 'rtl';
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState<LanguageCode>('en');
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();

  // Load user's preferred language from user metadata or localStorage
  useEffect(() => {
    const loadLanguage = () => {
      // First check user metadata if logged in
      if (user?.user_metadata?.preferred_language) {
        const savedLanguage = user.user_metadata.preferred_language as LanguageCode;
        if (SUPPORTED_LANGUAGES[savedLanguage]) {
          console.log('Loading language from user metadata:', savedLanguage);
          setCurrentLanguage(savedLanguage);
          return;
        }
      }

      // Fallback to localStorage for non-authenticated users or if no user preference
      const localLanguage = localStorage.getItem('preferred_language') as LanguageCode;
      if (localLanguage && SUPPORTED_LANGUAGES[localLanguage]) {
        console.log('Loading language from localStorage:', localLanguage);
        setCurrentLanguage(localLanguage);
        return;
      }

      // Default to English
      console.log('Using default language: en');
      setCurrentLanguage('en');
    };

    loadLanguage();
  }, [user]);

  // Update document direction and lang attribute when language changes
  useEffect(() => {
    const currentLang = SUPPORTED_LANGUAGES[currentLanguage];
    const direction = currentLang.rtl ? 'rtl' : 'ltr';

    // Update document attributes
    document.documentElement.setAttribute('dir', direction);
    document.documentElement.setAttribute('lang', currentLanguage);

    // Update body class for RTL styling
    if (currentLang.rtl) {
      document.body.classList.add('rtl');
      document.body.classList.remove('ltr');
    } else {
      document.body.classList.add('ltr');
      document.body.classList.remove('rtl');
    }
  }, [currentLanguage]);

  const setLanguage = async (language: LanguageCode) => {
    if (!SUPPORTED_LANGUAGES[language]) {
      console.error('Unsupported language:', language);
      return;
    }

    if (language === currentLanguage) {
      console.log('Language already set to:', language);
      return;
    }

    console.log('Changing language from', currentLanguage, 'to', language);
    setLoading(true);

    const previousLanguage = currentLanguage;

    try {
      // Update the current language immediately for UI responsiveness
      setCurrentLanguage(language);

      // Always save to localStorage for immediate persistence
      localStorage.setItem('preferred_language', language);

      // Save to user metadata if user is logged in
      if (user) {
        const { error } = await supabase.auth.updateUser({
          data: {
            ...user.user_metadata,
            preferred_language: language,
          }
        });

        if (error) {
          console.error('Error saving language preference to user metadata:', error);
          // Don't revert - localStorage still works
        } else {
          console.log('Language preference saved to user metadata:', language);
        }
      }

      console.log('Language successfully changed to:', language);
    } catch (error) {
      console.error('Error setting language:', error);
      // Revert to previous language on error
      setCurrentLanguage(previousLanguage);
      localStorage.setItem('preferred_language', previousLanguage);
    } finally {
      setLoading(false);
    }
  };

  // Reset language to default (useful for debugging)
  const resetLanguage = () => {
    console.log('Resetting language to default (en)');
    localStorage.removeItem('preferred_language');
    setCurrentLanguage('en');
  };

  // Translation function
  const t = (key: keyof typeof translations.en): string => {
    return translations[currentLanguage]?.[key] || translations.en[key] || key;
  };

  // RTL and direction helpers
  const isRTL = SUPPORTED_LANGUAGES[currentLanguage].rtl;
  const direction = isRTL ? 'rtl' : 'ltr';

  const value = {
    currentLanguage,
    setLanguage,
    resetLanguage,
    t,
    loading,
    isRTL,
    direction,
  };

  return <LanguageContext.Provider value={value}>{children}</LanguageContext.Provider>;
};
