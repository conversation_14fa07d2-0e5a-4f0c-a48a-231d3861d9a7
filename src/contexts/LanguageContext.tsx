import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

// Define supported languages
export const SUPPORTED_LANGUAGES = {
  en: { code: 'en', name: 'English', flag: '🇺🇸' },
  es: { code: 'es', name: '<PERSON>spa<PERSON><PERSON>', flag: '🇪🇸' },
  fr: { code: 'fr', name: 'Français', flag: '🇫🇷' },
  de: { code: 'de', name: '<PERSON><PERSON><PERSON>', flag: '🇩🇪' },
  it: { code: 'it', name: 'Italiano', flag: '🇮🇹' },
  pt: { code: 'pt', name: 'Português', flag: '🇵🇹' },
  zh: { code: 'zh', name: '中文', flag: '🇨🇳' },
  ja: { code: 'ja', name: '日本語', flag: '🇯🇵' },
  ko: { code: 'ko', name: '한국어', flag: '🇰🇷' },
  ar: { code: 'ar', name: 'العربية', flag: '🇸🇦' },
} as const;

export type LanguageCode = keyof typeof SUPPORTED_LANGUAGES;

// Basic translations - you can expand this
const translations = {
  en: {
    dashboard: 'Dashboard',
    messages: 'Messages',
    analytics: 'Analytics',
    settings: 'Settings',
    pricing: 'Pricing',
    profile: 'Profile',
    notifications: 'Notifications',
    security: 'Security',
    billing: 'Billing',
    apiKeys: 'API Keys',
    signOut: 'Sign Out',
    language: 'Language',
    selectLanguage: 'Select Language',
    save: 'Save',
    cancel: 'Cancel',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
  },
  es: {
    dashboard: 'Panel de Control',
    messages: 'Mensajes',
    analytics: 'Analíticas',
    settings: 'Configuración',
    pricing: 'Precios',
    profile: 'Perfil',
    notifications: 'Notificaciones',
    security: 'Seguridad',
    billing: 'Facturación',
    apiKeys: 'Claves API',
    signOut: 'Cerrar Sesión',
    language: 'Idioma',
    selectLanguage: 'Seleccionar Idioma',
    save: 'Guardar',
    cancel: 'Cancelar',
    loading: 'Cargando...',
    error: 'Error',
    success: 'Éxito',
  },
  fr: {
    dashboard: 'Tableau de Bord',
    messages: 'Messages',
    analytics: 'Analyses',
    settings: 'Paramètres',
    pricing: 'Tarifs',
    profile: 'Profil',
    notifications: 'Notifications',
    security: 'Sécurité',
    billing: 'Facturation',
    apiKeys: 'Clés API',
    signOut: 'Se Déconnecter',
    language: 'Langue',
    selectLanguage: 'Sélectionner la Langue',
    save: 'Enregistrer',
    cancel: 'Annuler',
    loading: 'Chargement...',
    error: 'Erreur',
    success: 'Succès',
  },
  de: {
    dashboard: 'Dashboard',
    messages: 'Nachrichten',
    analytics: 'Analytik',
    settings: 'Einstellungen',
    pricing: 'Preise',
    profile: 'Profil',
    notifications: 'Benachrichtigungen',
    security: 'Sicherheit',
    billing: 'Abrechnung',
    apiKeys: 'API-Schlüssel',
    signOut: 'Abmelden',
    language: 'Sprache',
    selectLanguage: 'Sprache Auswählen',
    save: 'Speichern',
    cancel: 'Abbrechen',
    loading: 'Laden...',
    error: 'Fehler',
    success: 'Erfolg',
  },
  it: {
    dashboard: 'Dashboard',
    messages: 'Messaggi',
    analytics: 'Analisi',
    settings: 'Impostazioni',
    pricing: 'Prezzi',
    profile: 'Profilo',
    notifications: 'Notifiche',
    security: 'Sicurezza',
    billing: 'Fatturazione',
    apiKeys: 'Chiavi API',
    signOut: 'Disconnetti',
    language: 'Lingua',
    selectLanguage: 'Seleziona Lingua',
    save: 'Salva',
    cancel: 'Annulla',
    loading: 'Caricamento...',
    error: 'Errore',
    success: 'Successo',
  },
  pt: {
    dashboard: 'Painel',
    messages: 'Mensagens',
    analytics: 'Análises',
    settings: 'Configurações',
    pricing: 'Preços',
    profile: 'Perfil',
    notifications: 'Notificações',
    security: 'Segurança',
    billing: 'Faturamento',
    apiKeys: 'Chaves API',
    signOut: 'Sair',
    language: 'Idioma',
    selectLanguage: 'Selecionar Idioma',
    save: 'Salvar',
    cancel: 'Cancelar',
    loading: 'Carregando...',
    error: 'Erro',
    success: 'Sucesso',
  },
  zh: {
    dashboard: '仪表板',
    messages: '消息',
    analytics: '分析',
    settings: '设置',
    pricing: '定价',
    profile: '个人资料',
    notifications: '通知',
    security: '安全',
    billing: '账单',
    apiKeys: 'API密钥',
    signOut: '退出',
    language: '语言',
    selectLanguage: '选择语言',
    save: '保存',
    cancel: '取消',
    loading: '加载中...',
    error: '错误',
    success: '成功',
  },
  ja: {
    dashboard: 'ダッシュボード',
    messages: 'メッセージ',
    analytics: '分析',
    settings: '設定',
    pricing: '料金',
    profile: 'プロフィール',
    notifications: '通知',
    security: 'セキュリティ',
    billing: '請求',
    apiKeys: 'APIキー',
    signOut: 'サインアウト',
    language: '言語',
    selectLanguage: '言語を選択',
    save: '保存',
    cancel: 'キャンセル',
    loading: '読み込み中...',
    error: 'エラー',
    success: '成功',
  },
  ko: {
    dashboard: '대시보드',
    messages: '메시지',
    analytics: '분석',
    settings: '설정',
    pricing: '가격',
    profile: '프로필',
    notifications: '알림',
    security: '보안',
    billing: '청구',
    apiKeys: 'API 키',
    signOut: '로그아웃',
    language: '언어',
    selectLanguage: '언어 선택',
    save: '저장',
    cancel: '취소',
    loading: '로딩 중...',
    error: '오류',
    success: '성공',
  },
  ar: {
    dashboard: 'لوحة التحكم',
    messages: 'الرسائل',
    analytics: 'التحليلات',
    settings: 'الإعدادات',
    pricing: 'التسعير',
    profile: 'الملف الشخصي',
    notifications: 'الإشعارات',
    security: 'الأمان',
    billing: 'الفواتير',
    apiKeys: 'مفاتيح API',
    signOut: 'تسجيل الخروج',
    language: 'اللغة',
    selectLanguage: 'اختر اللغة',
    save: 'حفظ',
    cancel: 'إلغاء',
    loading: 'جاري التحميل...',
    error: 'خطأ',
    success: 'نجح',
  },
};

interface LanguageContextType {
  currentLanguage: LanguageCode;
  setLanguage: (language: LanguageCode) => Promise<void>;
  t: (key: keyof typeof translations.en) => string;
  loading: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState<LanguageCode>('en');
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();

  // Load user's preferred language from user metadata
  useEffect(() => {
    if (user?.user_metadata?.preferred_language) {
      const savedLanguage = user.user_metadata.preferred_language as LanguageCode;
      if (SUPPORTED_LANGUAGES[savedLanguage]) {
        setCurrentLanguage(savedLanguage);
      }
    }
  }, [user]);

  const setLanguage = async (language: LanguageCode) => {
    if (!SUPPORTED_LANGUAGES[language]) {
      console.error('Unsupported language:', language);
      return;
    }

    setLoading(true);
    try {
      // Update the current language immediately for UI responsiveness
      setCurrentLanguage(language);

      // Save to user metadata if user is logged in
      if (user) {
        const { error } = await supabase.auth.updateUser({
          data: {
            preferred_language: language,
            ...user.user_metadata,
          }
        });

        if (error) {
          console.error('Error saving language preference:', error);
          // Optionally revert the language change on error
          // setCurrentLanguage(previousLanguage);
        }
      } else {
        // Save to localStorage for non-authenticated users
        localStorage.setItem('preferred_language', language);
      }
    } catch (error) {
      console.error('Error setting language:', error);
    } finally {
      setLoading(false);
    }
  };

  // Translation function
  const t = (key: keyof typeof translations.en): string => {
    return translations[currentLanguage]?.[key] || translations.en[key] || key;
  };

  const value = {
    currentLanguage,
    setLanguage,
    t,
    loading,
  };

  return <LanguageContext.Provider value={value}>{children}</LanguageContext.Provider>;
};
