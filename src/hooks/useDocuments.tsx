import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

export interface Document {
  id: string;
  user_id: string;
  bot_id?: string;
  file_name: string;
  file_url?: string;
  type: 'pdf' | 'txt' | 'docx' | 'chat';
  embedding_status: 'pending' | 'processed' | 'failed';
  created_at: string;
}

export const useDocuments = (botId?: string) => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    if (user) {
      fetchDocuments();
    }
  }, [user, botId]);

  const fetchDocuments = async () => {
    if (!user) return;

    try {
      let query = supabase
        .from('documents')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (botId) {
        query = query.eq('bot_id', botId);
      }

      const { data, error } = await query;

      if (error) throw error;
      setDocuments(data || []);
    } catch (error) {
      console.error('Error fetching documents:', error);
      toast({
        title: "Error",
        description: "Failed to load documents",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const uploadDocument = async (
    file: File,
    botId?: string,
    chatContent?: string
  ) => {
    if (!user) return null;

    try {
      let fileUrl = '';
      let fileName = file.name;
      let fileType: Document['type'] = 'txt';

      // Handle chat content
      if (chatContent) {
        fileName = 'chat-context.txt';
        fileType = 'chat';
      } else {
        // Handle file upload
        const fileExt = file.name.split('.').pop()?.toLowerCase();
        if (fileExt === 'pdf') fileType = 'pdf';
        else if (fileExt === 'docx') fileType = 'docx';
        else if (fileExt === 'txt') fileType = 'txt';

        // Upload file to Supabase Storage (we'll need to set up storage bucket)
        const fileName = `${user.id}/${Date.now()}-${file.name}`;
        // For now, we'll store a placeholder URL
        fileUrl = `documents/${fileName}`;
      }

      const { data, error } = await supabase
        .from('documents')
        .insert({
          user_id: user.id,
          bot_id: botId,
          file_name: fileName,
          file_url: fileUrl,
          type: fileType,
          embedding_status: 'pending'
        })
        .select()
        .single();

      if (error) throw error;
      
      setDocuments(prev => [data, ...prev]);
      toast({
        title: "Success",
        description: "Document uploaded successfully",
      });
      
      return data;
    } catch (error) {
      console.error('Error uploading document:', error);
      toast({
        title: "Error",
        description: "Failed to upload document",
        variant: "destructive",
      });
      return null;
    }
  };

  const deleteDocument = async (documentId: string) => {
    try {
      const { error } = await supabase
        .from('documents')
        .delete()
        .eq('id', documentId);

      if (error) throw error;
      
      setDocuments(prev => prev.filter(doc => doc.id !== documentId));
      toast({
        title: "Success",
        description: "Document deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting document:', error);
      toast({
        title: "Error",
        description: "Failed to delete document",
        variant: "destructive",
      });
    }
  };

  return {
    documents,
    loading,
    uploadDocument,
    deleteDocument,
    refetch: fetchDocuments
  };
};