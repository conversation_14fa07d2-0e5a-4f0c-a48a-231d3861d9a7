import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

export interface Bot {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  channel: 'whatsapp' | 'sms' | 'messenger';
  status: 'active' | 'paused' | 'archived';
  tone?: string;
  language?: string;
  training_status?: 'pending' | 'processing' | 'complete' | 'failed';
  training_updated_at?: string;
  embedding_context_id?: string;
  created_at: string;
  updated_at: string;
}

export const useBots = () => {
  const [bots, setBots] = useState<Bot[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    if (user) {
      fetchBots();
    }
  }, [user]);

  const fetchBots = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('bots')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setBots(data || []);
    } catch (error) {
      console.error('Error fetching bots:', error);
      toast({
        title: "Error",
        description: "Failed to load your bots",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const createBot = async (name: string, channel: Bot['channel']) => {
    if (!user) return null;

    try {
      const { data, error } = await supabase
        .from('bots')
        .insert({
          user_id: user.id,
          name,
          channel,
          status: 'active',
          training_status: 'pending',
          training_updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      
      setBots(prev => [data, ...prev]);
      toast({
        title: "Success",
        description: "Bot created successfully",
      });
      
      return data;
    } catch (error) {
      console.error('Error creating bot:', error);
      toast({
        title: "Error",
        description: "Failed to create bot",
        variant: "destructive",
      });
      return null;
    }
  };

  const updateBotStatus = async (botId: string, status: Bot['status']) => {
    try {
      const { error } = await supabase
        .from('bots')
        .update({ status })
        .eq('id', botId);

      if (error) throw error;
      
      setBots(prev => prev.map(bot => 
        bot.id === botId ? { ...bot, status } : bot
      ));
      
      toast({
        title: "Success",
        description: `Bot ${status} successfully`,
      });
    } catch (error) {
      console.error('Error updating bot status:', error);
      toast({
        title: "Error",
        description: "Failed to update bot status",
        variant: "destructive",
      });
    }
  };

  const deleteBot = async (botId: string) => {
    try {
      const { error } = await supabase
        .from('bots')
        .delete()
        .eq('id', botId);

      if (error) throw error;
      
      setBots(prev => prev.filter(bot => bot.id !== botId));
      toast({
        title: "Success",
        description: "Bot deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting bot:', error);
      toast({
        title: "Error",
        description: "Failed to delete bot",
        variant: "destructive",
      });
    }
  };

  const getBotById = async (botId: string): Promise<Bot | null> => {
    try {
      const { data, error } = await supabase
        .from('bots')
        .select('*')
        .eq('id', botId)
        .single();

      if (error) {
        console.error('Error fetching bot:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error fetching bot:', error);
      return null;
    }
  };

  const updateBot = async (botId: string, updates: Partial<Bot>) => {
    try {
      const { data, error } = await supabase
        .from('bots')
        .update(updates)
        .eq('id', botId)
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Update local state
      setBots(prev => prev.map(bot =>
        bot.id === botId ? { ...bot, ...data } : bot
      ));

      return data;
    } catch (error) {
      console.error('Error updating bot:', error);
      throw error;
    }
  };

  const retrainBot = async (botId: string) => {
    try {
      // Update training status to processing
      await updateBot(botId, {
        training_status: 'processing',
        training_updated_at: new Date().toISOString()
      });

      // TODO: Call backend API or Supabase Edge Function
      // const { data, error } = await supabase.functions.invoke('retrain-bot', {
      //   body: { bot_id: botId }
      // });

      // For now, simulate the training process
      setTimeout(async () => {
        try {
          await updateBot(botId, {
            training_status: 'complete',
            training_updated_at: new Date().toISOString()
          });

          toast({
            title: "Training Complete",
            description: "Your bot has been successfully retrained with the latest information.",
          });
        } catch (error) {
          await updateBot(botId, {
            training_status: 'failed',
            training_updated_at: new Date().toISOString()
          });

          toast({
            title: "Training Failed",
            description: "Bot training failed. Please try again.",
            variant: "destructive",
          });
        }
      }, 3000); // Simulate 3 second training

      return true;
    } catch (error) {
      console.error('Error retraining bot:', error);
      await updateBot(botId, {
        training_status: 'failed',
        training_updated_at: new Date().toISOString()
      });

      toast({
        title: "Training Failed",
        description: "Failed to start bot training. Please try again.",
        variant: "destructive",
      });
      return false;
    }
  };

  return {
    bots,
    loading,
    createBot,
    updateBotStatus,
    deleteBot,
    getBotById,
    updateBot,
    retrainBot,
    refetch: fetchBots
  };
};