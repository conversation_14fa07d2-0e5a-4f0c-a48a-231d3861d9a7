
import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';

export const usePhoneVerification = () => {
  const { user, loading: authLoading } = useAuth();
  const [needsPhoneVerification, setNeedsPhoneVerification] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkPhoneVerification = async () => {
      if (authLoading || !user) {
        setLoading(false);
        return;
      }

      try {
        // Temporarily disable phone verification check to avoid database issues
        // TODO: Re-enable when profiles table is properly set up
        setNeedsPhoneVerification(false);
        setLoading(false);

        /* Original code - commented out to avoid database errors
        // Check if user has phone number in profile
        const { data: profile, error } = await supabase
          .from('profiles')
          .select('phone_number')
          .eq('id', user.id)
          .single();

        if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
          console.error('Error checking phone verification:', error);
          setLoading(false);
          return;
        }

        // Check if phone number exists in profile or user metadata
        const hasPhoneInProfile = profile?.phone_number;
        const hasPhoneInMetadata = user.user_metadata?.phone_number;

        // User needs phone verification if they don't have a phone number
        // and they signed up via OAuth (not email/password)
        const isOAuthUser = user.app_metadata?.provider !== 'email';

        setNeedsPhoneVerification(
          isOAuthUser && !hasPhoneInProfile && !hasPhoneInMetadata
        );
        */
      } catch (error) {
        console.error('Error in phone verification check:', error);
        setNeedsPhoneVerification(false);
        setLoading(false);
      }
    };

    checkPhoneVerification();
  }, [user, authLoading]);

  return { needsPhoneVerification, loading };
};
