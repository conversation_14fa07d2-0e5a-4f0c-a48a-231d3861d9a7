import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

export interface Message {
  id: string;
  bot_id: string;
  sender: 'user' | 'bot';
  content: string;
  timestamp: string;
  channel: 'whatsapp' | 'sms' | 'messenger';
  recipient_number?: string;
  created_at: string;
}

export interface MessageWithBot extends Message {
  bot?: {
    id: string;
    name: string;
    channel: string;
  };
}

export const useMessages = (botId?: string) => {
  const [messages, setMessages] = useState<MessageWithBot[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    if (user) {
      fetchMessages();
    }
  }, [user, botId]);

  const fetchMessages = async () => {
    if (!user) return;

    try {
      let query = supabase
        .from('messages')
        .select(`
          *,
          bot:bots!messages_bot_id_fkey(id, name, channel)
        `)
        .order('timestamp', { ascending: false });

      if (botId && botId !== 'all') {
        query = query.eq('bot_id', botId);
      }

      const { data, error } = await query.limit(100);

      if (error) throw error;
      setMessages(data || []);
    } catch (error) {
      console.error('Error fetching messages:', error);
      toast({
        title: "Error",
        description: "Failed to load messages",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const sendMessage = async (
    botId: string,
    content: string,
    sender: 'user' | 'bot',
    channel: Message['channel'],
    recipientNumber?: string
  ) => {
    try {
      const { data, error } = await supabase
        .from('messages')
        .insert({
          bot_id: botId,
          sender,
          content,
          channel,
          recipient_number: recipientNumber,
          timestamp: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      
      setMessages(prev => [data, ...prev]);
      return data;
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive",
      });
      return null;
    }
  };

  return {
    messages,
    loading,
    sendMessage,
    refetch: fetchMessages
  };
};