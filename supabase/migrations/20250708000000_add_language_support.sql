-- Add language preference support to profiles table
ALTER TABLE public.profiles 
ADD COLUMN preferred_language TEXT DEFAULT 'en';

-- Add constraint to ensure only supported languages are used
ALTER TABLE public.profiles 
ADD CONSTRAINT check_supported_language 
CHECK (preferred_language IN ('en', 'es', 'fr', 'de', 'it', 'pt', 'zh', 'ja', 'ko', 'ar'));

-- Create index for language preference lookups
CREATE INDEX IF NOT EXISTS idx_profiles_preferred_language ON public.profiles(preferred_language);

-- Update the handle_new_user function to include language preference
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, first_name, last_name, email, phone_number, preferred_language)
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data->>'first_name',
    NEW.raw_user_meta_data->>'last_name',
    NEW.email,
    NEW.raw_user_meta_data->>'phone_number',
    COALESCE(NEW.raw_user_meta_data->>'preferred_language', 'en')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
