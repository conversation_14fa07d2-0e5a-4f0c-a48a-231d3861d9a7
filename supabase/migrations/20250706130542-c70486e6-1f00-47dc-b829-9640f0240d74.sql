-- Create enums for structured data
CREATE TYPE public.plan_type AS ENUM ('free', 'pro', 'business');
CREATE TYPE public.bot_channel AS ENUM ('whatsapp', 'sms', 'messenger');
CREATE TYPE public.bot_status AS ENUM ('active', 'paused', 'archived');
CREATE TYPE public.document_type AS ENUM ('pdf', 'txt', 'docx', 'chat');
CREATE TYPE public.embedding_status AS ENUM ('pending', 'processed', 'failed');
CREATE TYPE public.message_sender AS ENUM ('user', 'bot');
CREATE TYPE public.token_source AS ENUM ('generation', 'upload', 'chat', 'integration');

-- Extend existing profiles table to serve as users table
ALTER TABLE public.profiles 
ADD COLUMN plan public.plan_type DEFAULT 'free',
ADD COLUMN tokens_used INTEGER DEFAULT 0,
ADD COLUMN full_name TEXT GENERATED ALWAYS AS (
  CASE 
    WHEN first_name IS NOT NULL AND last_name IS NOT NULL 
    THEN first_name || ' ' || last_name
    WHEN first_name IS NOT NULL 
    THEN first_name
    WHEN last_name IS NOT NULL 
    THEN last_name
    ELSE NULL
  END
) STORED;

-- Create plans table for flexibility
CREATE TABLE public.plans (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  monthly_token_limit INTEGER NOT NULL,
  price_usd DECIMAL(10,2) NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Insert default plans
INSERT INTO public.plans (name, monthly_token_limit, price_usd) VALUES
('free', 1000, 0),
('pro', 10000, 29.99),
('business', 100000, 99.99);

-- Create bots table
CREATE TABLE public.bots (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  channel public.bot_channel NOT NULL,
  status public.bot_status NOT NULL DEFAULT 'active',
  embedding_context_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create documents table
CREATE TABLE public.documents (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  bot_id UUID REFERENCES public.bots(id) ON DELETE CASCADE,
  file_name TEXT NOT NULL,
  file_url TEXT,
  type public.document_type NOT NULL,
  embedding_status public.embedding_status NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create messages table
CREATE TABLE public.messages (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  bot_id UUID NOT NULL REFERENCES public.bots(id) ON DELETE CASCADE,
  sender public.message_sender NOT NULL,
  content TEXT NOT NULL,
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  channel public.bot_channel NOT NULL,
  recipient_number TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create token_usage table
CREATE TABLE public.token_usage (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  tokens_used INTEGER NOT NULL,
  description TEXT,
  source public.token_source NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX idx_bots_user_id ON public.bots(user_id);
CREATE INDEX idx_documents_user_id ON public.documents(user_id);
CREATE INDEX idx_documents_bot_id ON public.documents(bot_id);
CREATE INDEX idx_messages_bot_id ON public.messages(bot_id);
CREATE INDEX idx_messages_timestamp ON public.messages(timestamp);
CREATE INDEX idx_token_usage_user_id ON public.token_usage(user_id);

-- Enable Row Level Security on all tables
ALTER TABLE public.bots ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.token_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.plans ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for bots
CREATE POLICY "Users can view their own bots" ON public.bots
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own bots" ON public.bots
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own bots" ON public.bots
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own bots" ON public.bots
  FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for documents
CREATE POLICY "Users can view their own documents" ON public.documents
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own documents" ON public.documents
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own documents" ON public.documents
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own documents" ON public.documents
  FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for messages (via bot ownership)
CREATE POLICY "Users can view messages from their bots" ON public.messages
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.bots 
      WHERE bots.id = messages.bot_id 
      AND bots.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create messages for their bots" ON public.messages
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.bots 
      WHERE bots.id = messages.bot_id 
      AND bots.user_id = auth.uid()
    )
  );

-- Create RLS policies for token_usage
CREATE POLICY "Users can view their own token usage" ON public.token_usage
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own token usage" ON public.token_usage
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create RLS policies for plans (public read access)
CREATE POLICY "Plans are publicly readable" ON public.plans
  FOR SELECT USING (true);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_bots_updated_at
  BEFORE UPDATE ON public.bots
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Create function to track token usage
CREATE OR REPLACE FUNCTION public.track_token_usage(
  p_user_id UUID,
  p_tokens INTEGER,
  p_description TEXT,
  p_source public.token_source
) RETURNS VOID AS $$
BEGIN
  -- Insert token usage record
  INSERT INTO public.token_usage (user_id, tokens_used, description, source)
  VALUES (p_user_id, p_tokens, p_description, p_source);
  
  -- Update user's total token count
  UPDATE public.profiles 
  SET tokens_used = tokens_used + p_tokens
  WHERE id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;